{"name": "whistle.windsurf-modify-userstatus", "version": "1.11.0", "description": "A  whistle plugin for intercepting and modifying Windsurf (windsurf.ai) requests. It can modify user status and block telemetry/analytics requests to enhance privacy.", "main": "index.js", "scripts": {"dev": "tsc -w", "build": "tsc", "generate": "protoc --proto_path=./src --plugin=protoc-gen-ts_proto=.\\node_modules\\.bin\\protoc-gen-ts.cmd --ts_proto_out=./src/ ./src/proto/*.proto", "prepublishOnly": "npm run build", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["whistle", "plugin", "codeium", "proxy"], "author": "cipher", "license": "MIT", "repository": {"type": "git", "url": "***************:yongchang/whistle.windsurf-modify-userstatus.git"}, "bugs": {"url": "https://gitcode.com/yongchang/whistle.windsurf-modify-userstatus/issues"}, "homepage": "https://gitcode.com/yongchang/whistle.windsurf-modify-userstatus", "devDependencies": {"@protobuf-ts/plugin": "^2.9.3", "@types/node": "^20.11.16", "protobufjs": "^7.2.6", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "dependencies": {"@protobuf-ts/runtime": "^2.9.3", "long": "^5.3.0"}}