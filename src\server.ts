import {
  generateCommandModelConfigHex,
  generateUserStatusHex,
} from "./generate";

export default (
  server: Whistle.PluginServer,
  options: Whistle.PluginOptions
) => {
  // intercept sensitive endpoints
  const SENSITIVE_ENDPOINTS = [
    "RecordAsyncTelemetry",
    "RecordCascadeUsage",
    "RecordCommandUsage",
    "RecordCompletions",
    "RecordCortexGeneratorMetadata",
    "RecordCortexTrajectory",
    "RecordCortexTrajectoryStep",
    "RecordAnalyticsEvent",
    "RecordCortexExecutionMetadata",
  ];
  // handle http request
  server.on(
    "request",
    (req: Whistle.PluginServerRequest, res: Whistle.PluginServerResponse) => {
      const urlPath = req.fullUrl;

      if (SENSITIVE_ENDPOINTS.some((endpoint) => urlPath.includes(endpoint))) {
        res.setHeader("content-type", "application/proto");
        res.end(Buffer.from(""));
        console.log(
          `🛡️ intercept sensitive endpoint: ${urlPath.split("/").pop()}`
        );
      }

      if (urlPath.includes("GetUserStatus")) {
        interceptUserStatus(req, res);
      }

      if (urlPath.includes("GetCascadeModelConfigs")) {
        interceptModelConfig(req, res);
      }

      if (urlPath.includes("GetCommandModelConfigs")) {
        interceptCommandModelConfig(req, res);
      }
    }
  );

  // handle websocket request
  // server.on('upgrade', (req: Whistle.PluginServerRequest, socket: Whistle.PluginServerSocket) => {
  //   // do something
  //   req.passThrough();
  // });

  // // handle tunnel request
  // server.on('connect', (req: Whistle.PluginServerRequest, socket: Whistle.PluginServerSocket) => {
  //   // do something
  //   req.passThrough();
  // });
};

// set cors headers
const setCorsHeaders = (
  res: Whistle.PluginServerResponse,
  req: Whistle.PluginServerRequest
) => {
  res.setHeader("Access-Control-Allow-Credentials", "true");
  res.setHeader(
    "Access-Control-Allow-Headers",
    "connect-protocol-version,content-type,x-codeium-csrf-token"
  );
  res.setHeader("Access-Control-Allow-Methods", "POST");
  res.setHeader("Access-Control-Allow-Origin", req.headers.origin || "");
  res.setHeader(
    "Vary",
    "Origin, Access-Control-Request-Method, Access-Control-Request-Headers"
  );
};

// intercept GetUserStatus
function interceptUserStatus(
  req: Whistle.PluginServerRequest,
  res: Whistle.PluginServerResponse
) {
  const MODIFIED_CONTENT = generateUserStatusHex();
  if (req.method === "OPTIONS") {
    setCorsHeaders(res, req);
    res.statusCode = 204;
    res.end();
  } else {
    setCorsHeaders(res, req);
    res.setHeader("content-type", "application/proto");
    res.end(MODIFIED_CONTENT);

    console.info("🛡️ intercept and modify GetUserStatus response!");
  }
}

// intercept GetCascadeModelConfigs
function interceptModelConfig(
  req: Whistle.PluginServerRequest,
  res: Whistle.PluginServerResponse
) {
  // const MODIFIED_CONTENT = generateModelConfigHex();
  if (req.method === "OPTIONS") {
    setCorsHeaders(res, req);
    res.statusCode = 204;
    res.end();
  } else {
    setCorsHeaders(res, req);
    res.setHeader("content-type", "application/proto");
    res.end("");

    console.info("🛡️ intercept and modify GetCascadeModelConfigs response!");
  }
}

// intercept GetCommandModelConfigs
function interceptCommandModelConfig(
  req: Whistle.PluginServerRequest,
  res: Whistle.PluginServerResponse
) {
  const MODIFIED_CONTENT = generateCommandModelConfigHex();
  setCorsHeaders(res, req);
  res.setHeader("content-type", "application/proto");
  res.end(MODIFIED_CONTENT);

  console.info("🛡️ intercept and modify GetCommandModelConfigs response!");
}
