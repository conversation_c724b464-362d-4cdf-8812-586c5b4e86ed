# GetUserStatus 规则
https://server.codeium.com/exa.seat_management_pb.SeatManagementService/GetUserStatus windsurf-modify-userstatus://

# GetCascadeModelConfigsResponse 模型配置规则
# https://server.codeium.com/exa.api_server_pb.ApiServerService/GetCascadeModelConfigs windsurf-modify-userstatus://

# 敏感端点规则
https://server.codeium.com/exa.api_server_pb.ApiServerService/RecordAsyncTelemetry windsurf-modify-userstatus://
https://server.codeium.com/exa.api_server_pb.ApiServerService/RecordCascadeUsage windsurf-modify-userstatus://
https://server.codeium.com/exa.api_server_pb.ApiServerService/RecordCommandUsage windsurf-modify-userstatus://
https://server.codeium.com/exa.api_server_pb.ApiServerService/RecordCompletions windsurf-modify-userstatus://
https://server.codeium.com/exa.api_server_pb.ApiServerService/RecordCortexGeneratorMetadata windsurf-modify-userstatus://
https://server.codeium.com/exa.api_server_pb.ApiServerService/RecordCortexTrajectory windsurf-modify-userstatus://
https://server.codeium.com/exa.api_server_pb.ApiServerService/RecordCortexTrajectoryStep windsurf-modify-userstatus://
https://server.codeium.com/exa.product_analytics_pb.ProductAnalyticsService/RecordAnalyticsEvent windsurf-modify-userstatus://
https://server.codeium.com/exa.api_server_pb.ApiServerService/RecordCortexExecutionMetadata windsurf-modify-userstatus://

# GetCommandModelConfigsResponse 模型配置规则
https://server.codeium.com/exa.api_server_pb.ApiServerService/GetCommandModelConfigs windsurf-modify-userstatus://
