syntax = "proto3";

package exa.codeium_common_pb;

// 级联模型配置响应
message GetCascadeModelConfigsResponse {
    repeated ClientModelConfig client_model_configs = 1;    // 客户端模型配置列表
}

// command 模型配置响应
message GetCommandModelConfigsResponse {
    repeated ClientModelConfig client_model_configs = 1;    // 客户端模型配置列表
}

// 客户端模型配置
message ClientModelConfig {
    string label = 1;                                      // 标签名称
    ModelOrAlias model_or_alias = 2;                      // 模型或别名
    fixed32 credit_multiplier = 3;                         // 信用点数乘数
    bool disabled = 4;                                    // 是否禁用
    bool supports_images = 5;                             // 是否支持图片
    bool supports_legacy = 6;                             // 是否支持旧版本
    bool is_premium = 7;                                  // 是否为高级版本
    string beta_warning_message = 8;                      // Beta 版警告消息
    bool is_beta = 9;                                    // 是否为 Beta 版
    Provider provider = 10;                               // 提供商
    bool is_recommended = 11;                             // 是否推荐
    repeated Tier allowed_tiers = 12;                     // 允许的等级
}

// 模型或别名选择
message ModelOrAlias {
    oneof choice {
        Model model = 1;                                  // 模型
        Alias alias = 2;                                  // 别名
    }
}


enum Model {
    MODEL_UNSPECIFIED = 0;                               // 未指定模型
    MODEL_EMBED_6591 = 20;                               // 嵌入式模型
    MODEL_8341 = 33;                                     // 模型 8341
    MODEL_8528 = 42;                                     // 模型 8528
    MODEL_9024 = 41;                                     // 模型 9024
    MODEL_14602 = 112;                                   // 模型 14602
    MODEL_15133 = 115;                                   // 模型 15133
    MODEL_15302 = 119;                                   // 模型 15302
    MODEL_15335 = 121;                                   // 模型 15335
    MODEL_15336 = 122;                                   // 模型 15336
    MODEL_15931 = 167;                                   // 模型 15931
    MODEL_QUERY_9905 = 48;                               // 查询模型 9905
    MODEL_QUERY_11791 = 66;                              // 查询模型 11791
    MODEL_CHAT_11120 = 57;                               // 聊天模型 11120
    MODEL_CHAT_11121 = 58;                               // 聊天模型 11121
    MODEL_CHAT_12119 = 70;                               // 聊天模型 12119
    MODEL_CHAT_12121 = 69;                               // 聊天模型 12121
    MODEL_CHAT_12437 = 74;                               // 聊天模型 12437
    MODEL_CHAT_12491 = 76;                               // 聊天模型 12491
    MODEL_CHAT_12623 = 78;                               // 聊天模型 12623
    MODEL_CHAT_12950 = 79;                               // 聊天模型 12950
    MODEL_CHAT_12968 = 101;                              // 聊天模型 12968
    MODEL_CHAT_13404 = 102;                              // 聊天模型 13404
    MODEL_CHAT_13566 = 103;                              // 聊天模型 13566
    MODEL_CHAT_13930 = 108;                              // 聊天模型 13930
    MODEL_CHAT_14255 = 110;                              // 聊天模型 14255
    MODEL_CHAT_14256 = 111;                              // 聊天模型 14256
    MODEL_CHAT_14942 = 114;                              // 聊天模型 14942
    MODEL_CHAT_15305 = 120;                              // 聊天模型 15305
    MODEL_CHAT_15600 = 123;                              // 聊天模型 15600
    MODEL_CHAT_16801 = 124;                              // 聊天模型 16801
    MODEL_CHAT_16718 = 175;                              // 聊天模型 16718
    MODEL_CHAT_15729 = 168;                              // 聊天模型 15729
    MODEL_CHAT_16579 = 173;                              // 聊天模型 16579
    MODEL_CHAT_16579_CRUSOE = 174;                       // 聊天模型 16579_CRUSOE
    MODEL_CHAT_18805 = 181;                              // 聊天模型 18805
    MODEL_CHAT_18468 = 210;                              // 聊天模型 18468
    MODEL_CHAT_19484 = 233;                              // 聊天模型 19484
    MODEL_CHAT_20706 = 235;                              // 聊天模型 20706
    MODEL_CHAT_21779 = 245;                              // 聊天模型 21779
    MODEL_CHAT_19040 = 211;                              // 聊天模型 19040
    MODEL_CHAT_19820 = 229;                              // 聊天模型 19820
    MODEL_CHAT_19821 = 230;                              // 聊天模型 19821
    MODEL_CHAT_19821_CRUSOE = 244;                       // 聊天模型 19821_CRUSOE
    MODEL_CHAT_23310 = 269;                              // 聊天模型 23310
    MODEL_CHAT_19822 = 231;                              // 聊天模型 19822
    MODEL_CHAT_22798 = 255;                              // 聊天模型 22798
    MODEL_CHAT_22799 = 256;                              // 聊天模型 22799
    MODEL_CHAT_22800 = 257;                              // 聊天模型 22800
    MODEL_CHAT_23151 = 267;                              // 聊天模型 23151
    MODEL_CHAT_23152 = 268;                              // 聊天模型 23152
    MODEL_CASCADE_22893 = 270;                            // 级联模型 22893
    MODEL_CASCADE_20064 = 225;                            // 级联模型 20064
    MODEL_CASCADE_20065 = 236;                            // 级联模型 20065
    MODEL_CASCADE_20066 = 237;                            // 级联模型 20066
    MODEL_CASCADE_20067 = 238;                            // 级联模型 20067
    MODEL_CASCADE_20068 = 239;                            // 级联模型 20068
    MODEL_CASCADE_20069 = 240;                            // 级联模型 20069
    MODEL_CASCADE_20070 = 250;                            // 级联模型 20070
    MODEL_CASCADE_20071 = 251;                            // 级联模型 20071
    MODEL_CASCADE_20072 = 252;                            // 级联模型 20072
    MODEL_CASCADE_20073 = 253;                            // 级联模型 20073
    MODEL_CASCADE_20074 = 254;                            // 级联模型 20074
    MODEL_DEEPSEEK_V3_INTERNAL = 247;                     // 深度寻求 V3 内部
    MODEL_DEEPSEEK_V3_0324_INTERNAL = 248;                 // 深度寻求 V3 0324 内部
    MODEL_DEEPSEEK_R1_INTERNAL = 249;                     // 深度寻求 R1 内部
    MODEL_ANTHROPIC_WINDSURF_RESEARCH = 241;              // Anthropic Windsurf Research
    MODEL_ANTHROPIC_WINDSURF_RESEARCH_THINKING = 242;     // Anthropic Windsurf Research Thinking
    MODEL_DRAFT_11408 = 65;                               // 换代 11408
    MODEL_DRAFT_CHAT_11883 = 67;                          // 换代聊天 11883
    MODEL_DRAFT_CHAT_12196 = 72;                          // 换代聊天 12196
    MODEL_DRAFT_CHAT_12413 = 73;                          // 换代聊天 12413
    MODEL_DRAFT_CHAT_13175 = 104;                         // 换代聊天 13175
    MODEL_DRAFT_CHAT_19823 = 232;                         // 换代聊天 19823
    MODEL_DRAFT_CHAT_20707 = 243;                         // 换代聊天 20707
    MODEL_DRAFT_CHAT_22801 = 258;                         // 换代聊天 22801
    MODEL_DRAFT_CHAT_23508 = 273;                         // 换代聊天 23508
    MODEL_DRAFT_CASCADE_23672 = 274;                      // 换代级联 23672
    MODEL_CHAT_3_5_TURBO = 28;                            // 聊天 3.5 Turbo
    MODEL_CHAT_GPT_4 = 30;                                // 聊天 GPT 4
    MODEL_CHAT_GPT_4_1106_PREVIEW = 37;                   // 聊天 GPT 4 1106 预览
    MODEL_TEXT_EMBEDDING_OPENAI_ADA = 91;                 // 文本嵌入 OpenAI Ada
    MODEL_TEXT_EMBEDDING_OPENAI_3_SMALL = 163;            // 文本嵌入 OpenAI 3 小
    MODEL_TEXT_EMBEDDING_OPENAI_3_LARGE = 164;            // 文本嵌入 OpenAI 3 大
    MODEL_CHAT_GPT_4O_2024_05_13 = 71;                    // 聊天 GPT 4O 2024 05 13
    MODEL_CHAT_GPT_4O_2024_08_06 = 109;                   // 聊天 GPT 4O 2024 08 06
    MODEL_CHAT_GPT_4O_MINI_2024_07_18 = 113;              // 聊天 GPT 4O Mini 2024 07 18
    MODEL_CHAT_GPT_4_1_2025_04_14 = 259;                  // 聊天 GPT 4 1 2025 04 14
    MODEL_CHAT_GPT_4_1_MINI_2025_04_14 = 260;             // 聊天 GPT 4 1 Mini 2025 04 14
    MODEL_CHAT_GPT_4_1_NANO_2025_04_14 = 261;             // 聊天 GPT 4 1 Nano 2025 04 14
    MODEL_CHAT_O1_PREVIEW = 117;                          // 聊天 O1 预览
    MODEL_CHAT_O1_MINI = 118;                              // 聊天 O1 Mini
    MODEL_CHAT_O1 = 170;                                   // 聊天 O1
    MODEL_CHAT_O3_MINI = 207;                              // 聊天 O3 Mini
    MODEL_CHAT_O3_MINI_LOW = 213;                          // 聊天 O3 Mini Low
    MODEL_CHAT_O3_MINI_HIGH = 214;                         // 聊天 O3 Mini High
    MODEL_CHAT_O3 = 218;                                  // 聊天 O3
    MODEL_CHAT_O3_LOW = 262;                               // 聊天 O3 Low
    MODEL_CHAT_O3_HIGH = 263;                              // 聊天 O3 High
    MODEL_CHAT_O4_MINI = 264;                              // 聊天 O4 Mini
    MODEL_CHAT_O4_MINI_LOW = 265;                          // 聊天 O4 Mini Low
    MODEL_CHAT_O4_MINI_HIGH = 266;                         // 聊天 O4 Mini High
    MODEL_CHAT_GPT_4_5 = 228;                              // 聊天 GPT 4 5
    MODEL_GOOGLE_GEMINI_1_0_PRO = 61;                      // Google Gemini 1.0 Pro
    MODEL_GOOGLE_GEMINI_1_5_PRO = 62;                      // Google Gemini 1.5 Pro
    MODEL_GOOGLE_GEMINI_EXP_1206 = 183;                    // Google Gemini Exp 1206
    MODEL_GOOGLE_GEMINI_2_0_FLASH = 184;                   // Google Gemini 2.0 Flash
    MODEL_GOOGLE_GEMINI_2_5_PRO = 246;                     // Google Gemini 2.5 Pro
    MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_04_17 = 272;     // Google Gemini 2.5 Flash Preview 04 17
    MODEL_CLAUDE_3_OPUS_20240229 = 63;                    // Claude 3 Opus 20240229
    MODEL_CLAUDE_3_SONNET_20240229 = 64;                  // Claude 3 Sonnet 20240229
    MODEL_CLAUDE_3_5_SONNET_20240620 = 80;                // Claude 3.5 Sonnet 20240620
    MODEL_CLAUDE_3_5_HAIKU_20241022 = 171;                // Claude 3.5 Haiku 20241022
    MODEL_CLAUDE_3_5_SONNET_20241022 = 166;               // Claude 3.5 Sonnet 20241022
    MODEL_CLAUDE_3_HAIKU_20240307 = 172;                  // Claude 3 Haiku 20240307
    MODEL_CLAUDE_3_7_SONNET_20250219 = 226;               // Claude 3.7 Sonnet 20250219
    MODEL_CLAUDE_3_7_SONNET_20250219_THINKING = 227;      // Claude 3.7 Sonnet 20250219 Thinking
    MODEL_CLAUDE_4_OPUS = 290;                            // Claude 4.0 Opus - update 2025-06-21 windsurf 1.11.0
    MODEL_CLAUDE_4_SONNET = 281;                          // Claude 4.0 Sonnet - update 2025-06-21 windsurf 1.11.0
    // 新增模型 - update 2025-06-21 windsurf 1.11.0
    MODEL_GOOGLE_GEMINI_2_5_FLASH = 312;                  // Google Gemini 2.5 Flash
    MODEL_GOOGLE_GEMINI_2_5_FLASH_THINKING = 313;         // Google Gemini 2.5 Flash Thinking
    MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20 = 275;    // Google Gemini 2.5 Flash Preview 05-20
    MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20_THINKING = 276; // Google Gemini 2.5 Flash Preview 05-20 Thinking
    MODEL_O3_PRO_2025_06_10 = 294;                        // OpenAI O3 Pro 2025-06-10
    MODEL_O3_PRO_2025_06_10_LOW = 295;                    // OpenAI O3 Pro 2025-06-10 Low
    MODEL_O3_PRO_2025_06_10_HIGH = 296;                   // OpenAI O3 Pro 2025-06-10 High
    MODEL_TOGETHERAI_TEXT_EMBEDDING_M2_BERT = 81;          // TogetherAI Text Embedding M2 BERT
    MODEL_TOGETHERAI_LLAMA_3_1_8B_INSTRUCT = 165;          // TogetherAI Llama 3.1 8B Instruct
    MODEL_HUGGING_FACE_TEXT_EMBEDDING_M2_BERT = 82;        // Hugging Face Text Embedding M2 BERT
    MODEL_HUGGING_FACE_TEXT_EMBEDDING_UAE_CODE = 83;       // Hugging Face Text Embedding UAE Code
    MODEL_HUGGING_FACE_TEXT_EMBEDDING_BGE = 84;            // Hugging Face Text Embedding BGE
    MODEL_HUGGING_FACE_TEXT_EMBEDDING_BLADE = 85;          // Hugging Face Text Embedding Blade
    MODEL_HUGGING_FACE_TEXT_EMBEDDING_ARCTIC_LARGE = 86;   // Hugging Face Text Embedding Arctic Large
    MODEL_HUGGING_FACE_TEXT_EMBEDDING_E5_BASE = 87;        // Hugging Face Text Embedding E5 Base
    MODEL_HUGGING_FACE_TEXT_EMBEDDING_MXBAI = 88;          // Hugging Face Text Embedding MXBAI
    MODEL_LLAMA_3_1_8B_INSTRUCT = 106;                     // Llama 3.1 8B Instruct
    MODEL_LLAMA_3_1_70B_INSTRUCT = 107;                    // Llama 3.1 70B Instruct
    MODEL_LLAMA_3_1_405B_INSTRUCT = 105;                   // Llama 3.1 405B Instruct
    MODEL_LLAMA_3_3_70B_INSTRUCT = 208;                    // Llama 3.3 70B Instruct
    MODEL_LLAMA_3_3_70B_INSTRUCT_R1 = 209;                 // Llama 3.3 70B Instruct R1
    MODEL_LLAMA_3_1_70B_INSTRUCT_LONG_CONTEXT = 116;       // Llama 3.1 70B Instruct Long Context
    MODEL_LLAMA_3_1_8B_HERMES_3 = 176;                    // Llama 3.1 8B Hermes 3
    MODEL_LLAMA_3_1_70B_HERMES_3 = 177;                   // Llama 3.1 70B Hermes 3
    MODEL_QWEN_2_5_7B_INSTRUCT = 178;                      // Qwen 2.5 7B Instruct
    MODEL_QWEN_2_5_32B_INSTRUCT = 179;                     // Qwen 2.5 32B Instruct
    MODEL_QWEN_2_5_72B_INSTRUCT = 180;                     // Qwen 2.5 72B Instruct
    MODEL_QWEN_2_5_32B_INSTRUCT_R1 = 224;                  // Qwen 2.5 32B Instruct R1
    MODEL_NOMIC_TEXT_EMBEDDING_V1 = 89;                    // Nomic Text Embedding V1
    MODEL_NOMIC_TEXT_EMBEDDING_V1_5 = 90;                  // Nomic Text Embedding V1.5
    MODEL_MISTRAL_7B = 77;                                 // MISTRAL 7B
    MODEL_SALESFORCE_EMBEDDING_2R = 99;                    // Salesforce Embedding 2R
    MODEL_CUSTOM_VLLM = 182;                               // Custom VLLM
    MODEL_TEI_BGE_M3 = 92;                                 // TEI BGE M3
    MODEL_TEI_NOMIC_EMBED_TEXT_V1 = 93;                    // TEI NOMIC Embed Text V1
    MODEL_TEI_INTFLOAT_E5_LARGE_INSTRUCT = 94;             // TEI Intfloat E5 Large Instruct
    MODEL_TEI_SNOWFLAKE_ARCTIC_EMBED_L = 95;               // TEI Snowflake Arctic Embed L
    MODEL_TEI_UAE_CODE_LARGE_V1 = 96;                      // TEI UAE Code Large V1
    MODEL_TEI_B1ADE = 97;                                  // TEI B1ADE
    MODEL_TEI_WHEREISAI_UAE_LARGE_V1 = 98;                 // TEI Whereisai UAE Large V1
    MODEL_TEI_WHEREISAI_UAE_CODE_LARGE_V1 = 100;           // TEI Whereisai UAE Code Large V1
    MODEL_OPENAI_COMPATIBLE = 200;                         // OpenAI Compatible
    MODEL_ANTHROPIC_COMPATIBLE = 201;                      // Anthropic Compatible
    MODEL_VERTEX_COMPATIBLE = 202;                         // Vertex Compatible
    MODEL_BEDROCK_COMPATIBLE = 203;                        // Bedrock Compatible
    MODEL_AZURE_COMPATIBLE = 204;                          // Azure Compatible
    MODEL_DEEPSEEK_V3 = 205;                               // Deepseek V3
    MODEL_DEEPSEEK_R1 = 206;                               // Deepseek R1
    MODEL_DEEPSEEK_R1_SLOW = 215;                          // Deepseek R1 Slow
    MODEL_DEEPSEEK_R1_FAST = 216;                          // Deepseek R1 Fast
    MODEL_CUSTOM_OPEN_ROUTER = 185;                        // Custom Open Router
    MODEL_XAI_GROK_2 = 212;                                // XAI Grok 2
    MODEL_XAI_GROK_3 = 217;                                // XAI Grok 3
    MODEL_XAI_GROK_3_MINI_REASONING = 234;                  // XAI Grok 3 Mini Reasoning
    MODEL_PRIVATE_1 = 219;                                 // Private 1
    MODEL_PRIVATE_2 = 220;                                 // Private 2
    MODEL_PRIVATE_3 = 221;                                 // Private 3
    MODEL_PRIVATE_4 = 222;                                 // Private 4
    MODEL_PRIVATE_5 = 223;                                 // Private 5
}

enum Alias {
    MODEL_ALIAS_UNSPECIFIED = 0;                               // 未指定别名
    MODEL_ALIAS_CASCADE_BASE = 1;                              // Cascade Base
    MODEL_ALIAS_VISTA = 3;                                     // Vista
    MODEL_ALIAS_SHAMU = 4;                                     // Shamu
    MODEL_ALIAS_SWE_1 = 5;                                     // SWE 1
    MODEL_ALIAS_SWE_1_LITE = 6;                                // SWE 1 Lite
}

enum Provider {
    MODEL_PROVIDER_UNSPECIFIED = 0;                              // 未指定提供商
    MODEL_PROVIDER_WINDSURF = 1;                                 // Windsurf
    MODEL_PROVIDER_OPENAI = 2;                                   // OpenAI
    MODEL_PROVIDER_ANTHROPIC = 3;                                // Anthropic
    MODEL_PROVIDER_GOOGLE = 4;                                   // Google
    MODEL_PROVIDER_XAI = 5;                                      // XAI
    MODEL_PROVIDER_DEEPSEEK = 6;                                 // DeepSeek
}

enum Tier {
    MODEL_TIER_UNSPECIFIED = 0;                              // 未指定等级
    MODEL_TIER_TEAMS = 1;                                     // Teams
    MODEL_TIER_PRO = 2;                                       // Pro
    MODEL_TIER_TRIAL = 9;                                     // Trial
    MODEL_TIER_ENTERPRISE_SAAS = 3;                           // Enterprise SaaS
    MODEL_TIER_HYBRID = 4;                                    // Hybrid
    MODEL_TIER_ENTERPRISE_SELF_HOSTED = 5;                    // Enterprise Self-Hosted
    MODEL_TIER_WAITLIST_PRO = 6;                              // Waitlist Pro
    MODEL_TIER_TEAMS_ULTIMATE = 7;                            // Teams Ultimate
    MODEL_TIER_PRO_ULTIMATE = 8;                              // Pro Ultimate
    MODEL_TIER_ENTERPRISE_SELF_SERVE = 10;                     // Enterprise Self-Serve
}
