// @generated by protobuf-ts 2.10.0
// @generated from protobuf file "proto/user_status.proto" (package "exa.seat_management_pb", syntax proto3)
// tslint:disable
import type { BinaryWriteOptions } from "@protobuf-ts/runtime";
import type { IBinaryWriter } from "@protobuf-ts/runtime";
import { WireType } from "@protobuf-ts/runtime";
import type { BinaryReadOptions } from "@protobuf-ts/runtime";
import type { IBinaryReader } from "@protobuf-ts/runtime";
import { UnknownFieldHandler } from "@protobuf-ts/runtime";
import type { PartialMessage } from "@protobuf-ts/runtime";
import { reflectionMergePartial } from "@protobuf-ts/runtime";
import { MessageType } from "@protobuf-ts/runtime";
import { Timestamp } from "../google/protobuf/timestamp";
/**
 * 用户状态响应消息
 *
 * @generated from protobuf message exa.seat_management_pb.GetUserStatusResponse
 */
export interface GetUserStatusResponse {
    /**
     * @generated from protobuf field: exa.seat_management_pb.UserStatus user_status = 1;
     */
    userStatus?: UserStatus; // 用户状态信息
    /**
     * @generated from protobuf field: exa.seat_management_pb.PlanInfo plan_info = 2;
     */
    planInfo?: PlanInfo; // 计划信息
}
/**
 * 用户状态 - 包含用户的基本信息、权限和使用情况
 *
 * @generated from protobuf message exa.seat_management_pb.UserStatus
 */
export interface UserStatus {
    /**
     * @generated from protobuf field: bool pro = 1;
     */
    pro: boolean; // 是否为专业版用户
    /**
     * @generated from protobuf field: bool disable_telemetry = 2;
     */
    disableTelemetry: boolean; // 是否禁用遥测
    /**
     * @generated from protobuf field: string name = 3;
     */
    name: string; // 用户名称
    /**
     * @generated from protobuf field: bool ignore_chat_telemetry_setting = 4;
     */
    ignoreChatTelemetrySetting: boolean; // 是否忽略聊天遥测设置
    /**
     * @generated from protobuf field: string team_id = 5;
     */
    teamId: string; // 团队ID
    /**
     * @generated from protobuf field: exa.seat_management_pb.UserTeamStatus team_status = 6;
     */
    teamStatus: UserTeamStatus; // 团队状态
    /**
     * @generated from protobuf field: string email = 7;
     */
    email: string; // 用户邮箱
    /**
     * @generated from protobuf field: repeated exa.seat_management_pb.TeamsFeatures teams_features = 8;
     */
    teamsFeatures: TeamsFeatures[]; // 团队功能列表
    /**
     * @generated from protobuf field: repeated exa.seat_management_pb.UserFeatures user_features = 9;
     */
    userFeatures: UserFeatures[]; // 用户功能列表
    /**
     * @generated from protobuf field: repeated exa.seat_management_pb.Permission permissions = 11;
     */
    permissions: Permission[]; // 权限列表
    /**
     * @generated from protobuf field: exa.seat_management_pb.PlanInfo plan_info = 12;
     */
    planInfo?: PlanInfo; // 计划信息
    /**
     * @generated from protobuf field: exa.seat_management_pb.PlanStatus plan_status = 13;
     */
    planStatus?: PlanStatus; // 计划状态
    /**
     * @generated from protobuf field: int64 user_used_prompt_credits = 28;
     */
    userUsedPromptCredits: bigint; // 已使用的提示点数
    /**
     * @generated from protobuf field: int64 user_used_flow_credits = 29;
     */
    userUsedFlowCredits: bigint; // 已使用的流程点数
    /**
     * @generated from protobuf field: bool has_fingerprint_set = 30;
     */
    hasFingerprintSet: boolean; // 是否设置了指纹
    /**
     * @generated from protobuf field: bool has_used_windsurf = 31;
     */
    hasUsedWindsurf: boolean; // 是否使用过Windsurf
    /**
     * @generated from protobuf field: exa.seat_management_pb.TeamConfig team_config = 32;
     */
    teamConfig?: TeamConfig; // 默认团队配置 (no: 32) update 2025-04-03
    /**
     * @generated from protobuf field: exa.seat_management_pb.CascadeModelConfigData cascade_model_config_data = 33;
     */
    cascadeModelConfigData?: CascadeModelConfigData; // 级联模型配置数据 (no: 33) update 2025-06-21 windsurf 1.11.0
}
/**
 * 计划状态
 *
 * @generated from protobuf message exa.seat_management_pb.PlanStatus
 */
export interface PlanStatus {
    /**
     * @generated from protobuf field: exa.seat_management_pb.PlanInfo plan_info = 1;
     */
    planInfo?: PlanInfo; // 计划信息
    /**
     * @generated from protobuf field: google.protobuf.Timestamp plan_start = 2;
     */
    planStart?: Timestamp; // 计划开始时间
    /**
     * @generated from protobuf field: google.protobuf.Timestamp plan_end = 3;
     */
    planEnd?: Timestamp; // 计划结束时间
    /**
     * @generated from protobuf field: int32 available_flex_credits = 4;
     */
    availableFlexCredits: number; // 可用的弹性点数
    /**
     * @generated from protobuf field: int32 used_flow_credits = 5;
     */
    usedFlowCredits: number; // 已使用的流程点数
    /**
     * @generated from protobuf field: int32 used_prompt_credits = 6;
     */
    usedPromptCredits: number; // 已使用的提示点数
    /**
     * @generated from protobuf field: int32 used_flex_credits = 7;
     */
    usedFlexCredits: number; // 已使用的弹性点数
    /**
     * @generated from protobuf field: int32 available_prompt_credits = 8;
     */
    availablePromptCredits: number; // 可用的提示点数
    /**
     * @generated from protobuf field: int32 available_flow_credits = 9;
     */
    availableFlowCredits: number; // 可用的流程点数
}
/**
 * 计划信息 - 包含用户订阅计划的详细配置和限制
 *
 * @generated from protobuf message exa.seat_management_pb.PlanInfo
 */
export interface PlanInfo {
    /**
     * @generated from protobuf field: exa.seat_management_pb.TeamsTier teams_tier = 1;
     */
    teamsTier: TeamsTier; // 团队等级
    /**
     * @generated from protobuf field: string plan_name = 2;
     */
    planName: string; // 计划名称
    /**
     * @generated from protobuf field: bool has_autocomplete_fast_mode = 3;
     */
    hasAutocompleteFastMode: boolean; // 是否启用快速自动完成模式
    /**
     * @generated from protobuf field: bool allow_sticky_premium_models = 4;
     */
    allowStickyPremiumModels: boolean; // 是否允许固定高级模型
    /**
     * @generated from protobuf field: bool has_forge_access = 5;
     */
    hasForgeAccess: boolean; // 是否有Forge访问权限
    /**
     * @generated from protobuf field: int64 max_num_premium_chat_messages = 6;
     */
    maxNumPremiumChatMessages: bigint; // 高级聊天消息数量上限
    /**
     * @generated from protobuf field: int64 max_num_chat_input_tokens = 7;
     */
    maxNumChatInputTokens: bigint; // 聊天输入令牌数量上限
    /**
     * @generated from protobuf field: int64 max_custom_chat_instruction_characters = 8;
     */
    maxCustomChatInstructionCharacters: bigint; // 自定义聊天指令字符数上限
    /**
     * @generated from protobuf field: int64 max_num_pinned_context_items = 9;
     */
    maxNumPinnedContextItems: bigint; // 固定上下文项数量上限
    /**
     * @generated from protobuf field: int64 max_local_index_size = 10;
     */
    maxLocalIndexSize: bigint; // 本地索引大小上限
    /**
     * @generated from protobuf field: bool disable_code_snippet_telemetry = 11;
     */
    disableCodeSnippetTelemetry: boolean; // 是否禁用代码片段遥测
    /**
     * @generated from protobuf field: int32 monthly_prompt_credits = 12;
     */
    monthlyPromptCredits: number; // 每月提示点数
    /**
     * @generated from protobuf field: int32 monthly_flow_credits = 13;
     */
    monthlyFlowCredits: number; // 每月流程点数
    /**
     * @generated from protobuf field: int32 monthly_flex_credit_purchase_amount = 14;
     */
    monthlyFlexCreditPurchaseAmount: number; // 每月弹性点数购买额度
    /**
     * @generated from protobuf field: bool allow_premium_command_models = 15;
     */
    allowPremiumCommandModels: boolean; // 是否允许高级命令模型
    /**
     * @generated from protobuf field: bool is_enterprise = 16;
     */
    isEnterprise: boolean; // 是否为企业版
    /**
     * @generated from protobuf field: bool is_teams = 17;
     */
    isTeams: boolean; // 是否为团队版
    /**
     * @generated from protobuf field: bool can_buy_more_credits = 18;
     */
    canBuyMoreCredits: boolean; // 是否可以购买更多点数
    /**
     * @generated from protobuf field: bool cascade_web_search_enabled = 19;
     */
    cascadeWebSearchEnabled: boolean; // 是否启用级联网络搜索
    /**
     * @generated from protobuf field: bool can_customize_app_icon = 20;
     */
    canCustomizeAppIcon: boolean; // 是否可以自定义应用图标
    /**
     * @generated from protobuf field: repeated exa.seat_management_pb.AllowedModelConfig cascade_allowed_models_config = 21;
     */
    cascadeAllowedModelsConfig: AllowedModelConfig[]; // 允许的级联模型配置列表
    /**
     * @generated from protobuf field: bool cascade_can_auto_run_commands = 22;
     */
    cascadeCanAutoRunCommands: boolean; // 是否允许级联自动运行命令
    /**
     * @generated from protobuf field: bool has_tab_to_jump = 23;
     */
    hasTabToJump: boolean; // 是否启用tab跳转功能
    /**
     * @generated from protobuf field: exa.seat_management_pb.TeamConfig default_team_config = 24;
     */
    defaultTeamConfig?: TeamConfig; // 默认团队配置 update 2025-04-03
    /**
     * @generated from protobuf field: bool can_generate_commit_messages = 25;
     */
    canGenerateCommitMessages: boolean; // 是否可以生成提交消息 update 2025-04-03
    /**
     * @generated from protobuf field: int32 max_unclaimed_sites = 26;
     */
    maxUnclaimedSites: number; // 最大未认领站点数 update 2025-06-21 windsurf 1.10.5
    /**
     * update 2025-05-12 windsurf 1.8.2
     *
     * @generated from protobuf field: bool knowledge_base_enabled = 27;
     */
    knowledgeBaseEnabled: boolean; // 是否启用知识库
    /**
     * @generated from protobuf field: bool can_share_conversations = 28;
     */
    canShareConversations: boolean; // 是否可以分享对话
    /**
     * @generated from protobuf field: bool can_allow_cascade_in_background = 29;
     */
    canAllowCascadeInBackground: boolean; // 是否可以允许级联在后台运行
    /**
     * update 2025-06-21 windsurf 1.10.5 新增字段
     *
     * @generated from protobuf field: map<int32, exa.seat_management_pb.TeamFeatureConfig> default_team_features = 30;
     */
    defaultTeamFeatures: {
        [key: number]: TeamFeatureConfig;
    }; // 默认团队功能配置映射
    /**
     * @generated from protobuf field: bool browser_enabled = 31;
     */
    browserEnabled: boolean; // 是否启用浏览器功能
}
/**
 * 团队功能配置 - update 2025-06-21 windsurf 1.10.5
 *
 * @generated from protobuf message exa.seat_management_pb.TeamFeatureConfig
 */
export interface TeamFeatureConfig {
    /**
     * @generated from protobuf field: bool enabled = 1;
     */
    enabled: boolean; // 功能是否启用
    /**
     * @generated from protobuf field: map<string, string> config_params = 2;
     */
    configParams: {
        [key: string]: string;
    }; // 功能配置参数
    /**
     * @generated from protobuf field: int64 usage_limit = 3;
     */
    usageLimit: bigint; // 使用限制
    /**
     * @generated from protobuf field: string description = 4;
     */
    description: string; // 功能描述
}
/**
 * 级联模型配置数据 - update 2025-06-21 windsurf 1.11.0
 *
 * @generated from protobuf message exa.seat_management_pb.CascadeModelConfigData
 */
export interface CascadeModelConfigData {
    /**
     * @generated from protobuf field: repeated exa.seat_management_pb.ClientModelConfig client_model_configs = 1;
     */
    clientModelConfigs: ClientModelConfig[]; // 客户端模型配置列表
    /**
     * @generated from protobuf field: repeated exa.seat_management_pb.ClientModelSort client_model_sorts = 2;
     */
    clientModelSorts: ClientModelSort[]; // 客户端模型排序列表
    /**
     * @generated from protobuf field: exa.seat_management_pb.DefaultOverrideModelConfig default_override_model_config = 3;
     */
    defaultOverrideModelConfig?: DefaultOverrideModelConfig; // 默认覆盖模型配置 (可选)
}
/**
 * 客户端模型组 - update 2025-06-21 windsurf 1.11.0
 *
 * @generated from protobuf message exa.seat_management_pb.ClientModelGroup
 */
export interface ClientModelGroup {
    /**
     * @generated from protobuf field: string group_name = 1;
     */
    groupName: string; // 组名称
    /**
     * @generated from protobuf field: repeated string model_labels = 2;
     */
    modelLabels: string[]; // 模型标签列表
}
/**
 * 客户端模型排序 - update 2025-06-21 windsurf 1.11.0
 *
 * @generated from protobuf message exa.seat_management_pb.ClientModelSort
 */
export interface ClientModelSort {
    /**
     * @generated from protobuf field: string name = 1;
     */
    name: string; // 排序名称
    /**
     * @generated from protobuf field: repeated exa.seat_management_pb.ClientModelGroup groups = 2;
     */
    groups: ClientModelGroup[]; // 模型组列表
}
/**
 * 默认覆盖模型配置 - update 2025-06-21 windsurf 1.11.0
 *
 * @generated from protobuf message exa.seat_management_pb.DefaultOverrideModelConfig
 */
export interface DefaultOverrideModelConfig {
    /**
     * @generated from protobuf field: exa.seat_management_pb.ModelOrAlias model_or_alias = 1;
     */
    modelOrAlias?: ModelOrAlias; // 模型或别名
    /**
     * @generated from protobuf field: string version_id = 2;
     */
    versionId: string; // 版本ID
}
/**
 * 团队配置
 *
 * @generated from protobuf message exa.seat_management_pb.TeamConfig
 */
export interface TeamConfig {
    /**
     * @generated from protobuf field: string team_id = 1;
     */
    teamId: string; // 团队ID
    /**
     * @generated from protobuf field: int64 user_prompt_credit_cap = 2;
     */
    userPromptCreditCap: bigint; // 用户提示点数上限
    /**
     * @generated from protobuf field: int64 user_flow_credit_cap = 3;
     */
    userFlowCreditCap: bigint; // 用户流程点数上限
    /**
     * @generated from protobuf field: bool auto_provision_cascade_seat = 4;
     */
    autoProvisionCascadeSeat: boolean; // 自动配置级联席位
    /**
     * @generated from protobuf field: bool allow_mcp_servers = 5;
     */
    allowMcpServers: boolean; // 允许MCP服务器
    /**
     * @generated from protobuf field: bool allow_auto_run_commands = 7;
     */
    allowAutoRunCommands: boolean; // 允许自动运行命令
    /**
     * @generated from protobuf field: bool allow_custom_recipes = 8;
     */
    allowCustomRecipes: boolean; // 允许自定义配方
    /**
     * @generated from protobuf field: int64 max_unclaimed_sites = 9;
     */
    maxUnclaimedSites: bigint; // 最大未认领站点数
    /**
     * @generated from protobuf field: bool allow_app_deployments = 10;
     */
    allowAppDeployments: boolean; // 允许应用部署
    /**
     * @generated from protobuf field: int64 max_new_sites_per_day = 11;
     */
    maxNewSitesPerDay: bigint; // 每日最大新站点数
}
/**
 * 允许的模型配置
 *
 * @generated from protobuf message exa.seat_management_pb.AllowedModelConfig
 */
export interface AllowedModelConfig {
    /**
     * @generated from protobuf field: exa.seat_management_pb.ModelOrAlias model_or_alias = 1;
     */
    modelOrAlias?: ModelOrAlias; // 模型或别名
    /**
     * @generated from protobuf field: float credit_multiplier = 2;
     */
    creditMultiplier: number; // 点数乘数
}
/**
 * 模型或别名选择
 *
 * @generated from protobuf message exa.seat_management_pb.ModelOrAlias
 */
export interface ModelOrAlias {
    /**
     * @generated from protobuf oneof: choice
     */
    choice: {
        oneofKind: "model";
        /**
         * @generated from protobuf field: exa.seat_management_pb.Model model = 1;
         */
        model: Model; // 模型
    } | {
        oneofKind: "alias";
        /**
         * @generated from protobuf field: exa.seat_management_pb.Alias alias = 2;
         */
        alias: Alias; // 别名
    } | {
        oneofKind: undefined;
    };
}
/**
 * 客户端模型配置 - update 2025-06-21 windsurf 1.11.0
 *
 * @generated from protobuf message exa.seat_management_pb.ClientModelConfig
 */
export interface ClientModelConfig {
    /**
     * @generated from protobuf field: string label = 1;
     */
    label: string; // 模型标签
    /**
     * @generated from protobuf field: exa.seat_management_pb.ModelOrAlias model_or_alias = 2;
     */
    modelOrAlias?: ModelOrAlias; // 模型或别名
    /**
     * @generated from protobuf field: float credit_multiplier = 3;
     */
    creditMultiplier: number; // 点数乘数
    /**
     * @generated from protobuf field: exa.seat_management_pb.ModelPricingType pricing_type = 13;
     */
    pricingType: ModelPricingType; // 定价类型 (no: 13)
    /**
     * @generated from protobuf field: bool disabled = 4;
     */
    disabled: boolean; // 是否禁用
    /**
     * @generated from protobuf field: bool supports_images = 5;
     */
    supportsImages: boolean; // 是否支持图像
    /**
     * @generated from protobuf field: bool supports_legacy = 6;
     */
    supportsLegacy: boolean; // 是否支持旧版
    /**
     * @generated from protobuf field: bool is_premium = 7;
     */
    isPremium: boolean; // 是否为高级版
    /**
     * @generated from protobuf field: string beta_warning_message = 8;
     */
    betaWarningMessage: string; // Beta 警告消息
    /**
     * @generated from protobuf field: bool is_beta = 9;
     */
    isBeta: boolean; // 是否为 Beta 版
    /**
     * @generated from protobuf field: exa.seat_management_pb.Provider provider = 10;
     */
    provider: Provider; // 提供商
    /**
     * @generated from protobuf field: bool is_recommended = 11;
     */
    isRecommended: boolean; // 是否推荐
    /**
     * @generated from protobuf field: repeated exa.seat_management_pb.TeamsTier allowed_tiers = 12;
     */
    allowedTiers: TeamsTier[]; // 允许的等级列表
}
/**
 * 模型枚举
 *
 * @generated from protobuf enum exa.seat_management_pb.Model
 */
export enum Model {
    /**
     * 未指定模型
     *
     * @generated from protobuf enum value: MODEL_UNSPECIFIED = 0;
     */
    UNSPECIFIED = 0,
    /**
     * 仅包含必要的值，完整定义在model_config.proto中
     * Claude 模型 - update 2025-06-21 windsurf 1.11.0
     *
     * Claude 3.7 Sonnet
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_3_7_SONNET_20250219 = 226;
     */
    CLAUDE_3_7_SONNET_20250219 = 226,
    /**
     * Claude 4.0 Opus
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_4_OPUS = 290;
     */
    CLAUDE_4_OPUS = 290,
    /**
     * Claude 4.0 Sonnet
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_4_SONNET = 281;
     */
    CLAUDE_4_SONNET = 281,
    /**
     * Google 模型 - update 2025-06-21 windsurf 1.11.0
     *
     * Google Gemini 1.0 Pro
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_1_0_PRO = 61;
     */
    GOOGLE_GEMINI_1_0_PRO = 61,
    /**
     * Google Gemini 1.5 Pro
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_1_5_PRO = 62;
     */
    GOOGLE_GEMINI_1_5_PRO = 62,
    /**
     * Google Gemini 2.0 Flash
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_2_0_FLASH = 184;
     */
    GOOGLE_GEMINI_2_0_FLASH = 184,
    /**
     * Google Gemini 2.5 Pro
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_2_5_PRO = 246;
     */
    GOOGLE_GEMINI_2_5_PRO = 246,
    /**
     * Google Gemini 2.5 Flash
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_2_5_FLASH = 312;
     */
    GOOGLE_GEMINI_2_5_FLASH = 312,
    /**
     * Google Gemini 2.5 Flash Thinking
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_2_5_FLASH_THINKING = 313;
     */
    GOOGLE_GEMINI_2_5_FLASH_THINKING = 313,
    /**
     * Google Gemini Exp 1206
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_EXP_1206 = 183;
     */
    GOOGLE_GEMINI_EXP_1206 = 183,
    /**
     * Google Gemini 2.5 Flash Preview 05-20
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20 = 275;
     */
    GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20 = 275,
    /**
     * Google Gemini 2.5 Flash Preview 05-20 Thinking
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20_THINKING = 276;
     */
    GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20_THINKING = 276,
    /**
     * DeepSeek 模型 - update 2025-06-21 windsurf 1.11.0
     *
     * DeepSeek V3
     *
     * @generated from protobuf enum value: MODEL_DEEPSEEK_V3 = 205;
     */
    DEEPSEEK_V3 = 205,
    /**
     * DeepSeek R1
     *
     * @generated from protobuf enum value: MODEL_DEEPSEEK_R1 = 206;
     */
    DEEPSEEK_R1 = 206,
    /**
     * DeepSeek R1 Fast
     *
     * @generated from protobuf enum value: MODEL_DEEPSEEK_R1_FAST = 216;
     */
    DEEPSEEK_R1_FAST = 216,
    /**
     * DeepSeek R1 Slow
     *
     * @generated from protobuf enum value: MODEL_DEEPSEEK_R1_SLOW = 215;
     */
    DEEPSEEK_R1_SLOW = 215,
    /**
     * OpenAI 模型 - update 2025-06-21 windsurf 1.11.0
     *
     * OpenAI O3 Pro 2025-06-10
     *
     * @generated from protobuf enum value: MODEL_O3_PRO_2025_06_10 = 294;
     */
    O3_PRO_2025_06_10 = 294,
    /**
     * OpenAI O3 Pro 2025-06-10 Low
     *
     * @generated from protobuf enum value: MODEL_O3_PRO_2025_06_10_LOW = 295;
     */
    O3_PRO_2025_06_10_LOW = 295,
    /**
     * OpenAI O3 Pro 2025-06-10 High
     *
     * @generated from protobuf enum value: MODEL_O3_PRO_2025_06_10_HIGH = 296;
     */
    O3_PRO_2025_06_10_HIGH = 296,
    /**
     * 新增模型 - update 2025-06-21 windsurf 1.12.1
     *
     * Kimi K2
     *
     * @generated from protobuf enum value: MODEL_KIMI_K2 = 323;
     */
    KIMI_K2 = 323,
    /**
     * Qwen 3 235B Instruct
     *
     * @generated from protobuf enum value: MODEL_QWEN_3_235B_INSTRUCT = 324;
     */
    QWEN_3_235B_INSTRUCT = 324,
    /**
     * Qwen 3 Coder 480B Instruct
     *
     * @generated from protobuf enum value: MODEL_QWEN_3_CODER_480B_INSTRUCT = 325;
     */
    QWEN_3_CODER_480B_INSTRUCT = 325,
    /**
     * GPT OSS 120B
     *
     * @generated from protobuf enum value: MODEL_GPT_OSS_120B = 326;
     */
    GPT_OSS_120B = 326,
    /**
     * Qwen 3 Coder 480B Instruct Fast
     *
     * @generated from protobuf enum value: MODEL_QWEN_3_CODER_480B_INSTRUCT_FAST = 327;
     */
    QWEN_3_CODER_480B_INSTRUCT_FAST = 327,
    /**
     * Claude 4.1 Opus
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_4_1_OPUS = 328;
     */
    CLAUDE_4_1_OPUS = 328,
    /**
     * Claude 4.1 Opus Thinking
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_4_1_OPUS_THINKING = 329;
     */
    CLAUDE_4_1_OPUS_THINKING = 329,
    /**
     * GPT-5 Nano
     *
     * @generated from protobuf enum value: MODEL_GPT_5_NANO = 337;
     */
    GPT_5_NANO = 337,
    /**
     * GPT-5 Minimal
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_5_MINIMAL = 338;
     */
    CHAT_GPT_5_MINIMAL = 338,
    /**
     * GPT-5 Low Reasoning
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_5_LOW = 339;
     */
    CHAT_GPT_5_LOW = 339,
    /**
     * GPT-5 Standard
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_5 = 340;
     */
    CHAT_GPT_5 = 340,
    /**
     * GPT-5 High Reasoning
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_5_HIGH = 341;
     */
    CHAT_GPT_5_HIGH = 341
}
/**
 * 别名枚举
 *
 * @generated from protobuf enum exa.seat_management_pb.Alias
 */
export enum Alias {
    /**
     * 未指定别名
     *
     * @generated from protobuf enum value: ALIAS_UNSPECIFIED = 0;
     */
    ALIAS_UNSPECIFIED = 0,
    /**
     * Cascade Base
     *
     * @generated from protobuf enum value: MODEL_ALIAS_CASCADE_BASE = 1;
     */
    MODEL_ALIAS_CASCADE_BASE = 1
}
/**
 * 用户团队状态
 *
 * @generated from protobuf enum exa.seat_management_pb.UserTeamStatus
 */
export enum UserTeamStatus {
    /**
     * 未指定状态
     *
     * @generated from protobuf enum value: USER_TEAM_STATUS_UNSPECIFIED = 0;
     */
    UNSPECIFIED = 0,
    /**
     * 待定状态
     *
     * @generated from protobuf enum value: USER_TEAM_STATUS_PENDING = 1;
     */
    PENDING = 1,
    /**
     * 已批准状态
     *
     * @generated from protobuf enum value: USER_TEAM_STATUS_APPROVED = 2;
     */
    APPROVED = 2,
    /**
     * 已拒绝状态
     *
     * @generated from protobuf enum value: USER_TEAM_STATUS_REJECTED = 3;
     */
    REJECTED = 3
}
/**
 * 团队等级
 *
 * @generated from protobuf enum exa.seat_management_pb.TeamsTier
 */
export enum TeamsTier {
    /**
     * 未指定等级
     *
     * @generated from protobuf enum value: TEAMS_TIER_UNSPECIFIED = 0;
     */
    UNSPECIFIED = 0,
    /**
     * 团队版
     *
     * @generated from protobuf enum value: TEAMS_TIER_TEAMS = 1;
     */
    TEAMS = 1,
    /**
     * 专业版
     *
     * @generated from protobuf enum value: TEAMS_TIER_PRO = 2;
     */
    PRO = 2,
    /**
     * 试用版
     *
     * @generated from protobuf enum value: TEAMS_TIER_TRIAL = 9;
     */
    TRIAL = 9,
    /**
     * 企业SaaS版
     *
     * @generated from protobuf enum value: TEAMS_TIER_ENTERPRISE_SAAS = 3;
     */
    ENTERPRISE_SAAS = 3,
    /**
     * 混合版
     *
     * @generated from protobuf enum value: TEAMS_TIER_HYBRID = 4;
     */
    HYBRID = 4,
    /**
     * 企业自托管版
     *
     * @generated from protobuf enum value: TEAMS_TIER_ENTERPRISE_SELF_HOSTED = 5;
     */
    ENTERPRISE_SELF_HOSTED = 5,
    /**
     * 专业版等待列表
     *
     * @generated from protobuf enum value: TEAMS_TIER_WAITLIST_PRO = 6;
     */
    WAITLIST_PRO = 6,
    /**
     * 团队旗舰版
     *
     * @generated from protobuf enum value: TEAMS_TIER_TEAMS_ULTIMATE = 7;
     */
    TEAMS_ULTIMATE = 7,
    /**
     * 专业旗舰版
     *
     * @generated from protobuf enum value: TEAMS_TIER_PRO_ULTIMATE = 8;
     */
    PRO_ULTIMATE = 8
}
/**
 * 用户特性
 *
 * @generated from protobuf enum exa.seat_management_pb.UserFeatures
 */
export enum UserFeatures {
    /**
     * 未指定特性
     *
     * @generated from protobuf enum value: USER_FEATURES_UNSPECIFIED = 0;
     */
    UNSPECIFIED = 0,
    /**
     * Cortex特性
     *
     * @generated from protobuf enum value: USER_FEATURES_CORTEX = 1;
     */
    CORTEX = 1,
    /**
     * Cortex测试特性
     *
     * @generated from protobuf enum value: USER_FEATURES_CORTEX_TEST = 2;
     */
    CORTEX_TEST = 2
}
/**
 * 团队特性
 *
 * @generated from protobuf enum exa.seat_management_pb.TeamsFeatures
 */
export enum TeamsFeatures {
    /**
     * 未指定特性
     *
     * @generated from protobuf enum value: TEAMS_FEATURES_UNSPECIFIED = 0;
     */
    UNSPECIFIED = 0,
    /**
     * 单点登录
     *
     * @generated from protobuf enum value: TEAMS_FEATURES_SSO = 1;
     */
    SSO = 1,
    /**
     * 归因功能
     *
     * @generated from protobuf enum value: TEAMS_FEATURES_ATTRIBUTION = 2;
     */
    ATTRIBUTION = 2,
    /**
     * PHI功能
     *
     * @generated from protobuf enum value: TEAMS_FEATURES_PHI = 3;
     */
    PHI = 3,
    /**
     * Cortex功能
     *
     * @generated from protobuf enum value: TEAMS_FEATURES_CORTEX = 4;
     */
    CORTEX = 4,
    /**
     * OpenAI禁用
     *
     * @generated from protobuf enum value: TEAMS_FEATURES_OPENAI_DISABLED = 5;
     */
    OPENAI_DISABLED = 5,
    /**
     * 远程索引禁用
     *
     * @generated from protobuf enum value: TEAMS_FEATURES_REMOTE_INDEXING_DISABLED = 6;
     */
    REMOTE_INDEXING_DISABLED = 6,
    /**
     * API密钥启用
     *
     * @generated from protobuf enum value: TEAMS_FEATURES_API_KEY_ENABLED = 7;
     */
    API_KEY_ENABLED = 7
}
/**
 * 权限
 *
 * @generated from protobuf enum exa.seat_management_pb.Permission
 */
export enum Permission {
    /**
     * 未指定权限
     *
     * @generated from protobuf enum value: PERMISSION_UNSPECIFIED = 0;
     */
    UNSPECIFIED = 0,
    /**
     * 归因读取权限
     *
     * @generated from protobuf enum value: PERMISSION_ATTRIBUTION_READ = 1;
     */
    ATTRIBUTION_READ = 1,
    /**
     * 分析读取权限
     *
     * @generated from protobuf enum value: PERMISSION_ANALYTICS_READ = 2;
     */
    ANALYTICS_READ = 2,
    /**
     * 许可证读取权限
     *
     * @generated from protobuf enum value: PERMISSION_LICENSE_READ = 3;
     */
    LICENSE_READ = 3,
    /**
     * 团队用户读取权限
     *
     * @generated from protobuf enum value: PERMISSION_TEAM_USER_READ = 4;
     */
    TEAM_USER_READ = 4,
    /**
     * 团队用户更新权限
     *
     * @generated from protobuf enum value: PERMISSION_TEAM_USER_UPDATE = 5;
     */
    TEAM_USER_UPDATE = 5,
    /**
     * 团队用户删除权限
     *
     * @generated from protobuf enum value: PERMISSION_TEAM_USER_DELETE = 6;
     */
    TEAM_USER_DELETE = 6,
    /**
     * 团队用户邀请权限
     *
     * @generated from protobuf enum value: PERMISSION_TEAM_USER_INVITE = 17;
     */
    TEAM_USER_INVITE = 17,
    /**
     * 索引读取权限
     *
     * @generated from protobuf enum value: PERMISSION_INDEXING_READ = 7;
     */
    INDEXING_READ = 7,
    /**
     * 索引创建权限
     *
     * @generated from protobuf enum value: PERMISSION_INDEXING_CREATE = 8;
     */
    INDEXING_CREATE = 8,
    /**
     * 索引更新权限
     *
     * @generated from protobuf enum value: PERMISSION_INDEXING_UPDATE = 9;
     */
    INDEXING_UPDATE = 9,
    /**
     * 索引删除权限
     *
     * @generated from protobuf enum value: PERMISSION_INDEXING_DELETE = 10;
     */
    INDEXING_DELETE = 10,
    /**
     * 索引管理权限
     *
     * @generated from protobuf enum value: PERMISSION_INDEXING_MANAGEMENT = 27;
     */
    INDEXING_MANAGEMENT = 27,
    /**
     * 微调读取权限
     *
     * @generated from protobuf enum value: PERMISSION_FINETUNING_READ = 19;
     */
    FINETUNING_READ = 19,
    /**
     * 微调创建权限
     *
     * @generated from protobuf enum value: PERMISSION_FINETUNING_CREATE = 20;
     */
    FINETUNING_CREATE = 20,
    /**
     * 微调更新权限
     *
     * @generated from protobuf enum value: PERMISSION_FINETUNING_UPDATE = 21;
     */
    FINETUNING_UPDATE = 21,
    /**
     * 微调删除权限
     *
     * @generated from protobuf enum value: PERMISSION_FINETUNING_DELETE = 22;
     */
    FINETUNING_DELETE = 22,
    /**
     * SSO读取权限
     *
     * @generated from protobuf enum value: PERMISSION_SSO_READ = 11;
     */
    SSO_READ = 11,
    /**
     * SSO写入权限
     *
     * @generated from protobuf enum value: PERMISSION_SSO_WRITE = 12;
     */
    SSO_WRITE = 12,
    /**
     * 服务密钥读取权限
     *
     * @generated from protobuf enum value: PERMISSION_SERVICE_KEY_READ = 13;
     */
    SERVICE_KEY_READ = 13,
    /**
     * 服务密钥创建权限
     *
     * @generated from protobuf enum value: PERMISSION_SERVICE_KEY_CREATE = 14;
     */
    SERVICE_KEY_CREATE = 14,
    /**
     * 服务密钥更新权限
     *
     * @generated from protobuf enum value: PERMISSION_SERVICE_KEY_UPDATE = 28;
     */
    SERVICE_KEY_UPDATE = 28,
    /**
     * 服务密钥删除权限
     *
     * @generated from protobuf enum value: PERMISSION_SERVICE_KEY_DELETE = 15;
     */
    SERVICE_KEY_DELETE = 15,
    /**
     * 角色读取权限
     *
     * @generated from protobuf enum value: PERMISSION_ROLE_READ = 23;
     */
    ROLE_READ = 23,
    /**
     * 角色创建权限
     *
     * @generated from protobuf enum value: PERMISSION_ROLE_CREATE = 24;
     */
    ROLE_CREATE = 24,
    /**
     * 角色更新权限
     *
     * @generated from protobuf enum value: PERMISSION_ROLE_UPDATE = 25;
     */
    ROLE_UPDATE = 25,
    /**
     * 角色删除权限
     *
     * @generated from protobuf enum value: PERMISSION_ROLE_DELETE = 26;
     */
    ROLE_DELETE = 26,
    /**
     * 账单读取权限
     *
     * @generated from protobuf enum value: PERMISSION_BILLING_READ = 16;
     */
    BILLING_READ = 16,
    /**
     * 账单写入权限
     *
     * @generated from protobuf enum value: PERMISSION_BILLING_WRITE = 18;
     */
    BILLING_WRITE = 18,
    /**
     * 外部聊天更新权限
     *
     * @generated from protobuf enum value: PERMISSION_EXTERNAL_CHAT_UPDATE = 29;
     */
    EXTERNAL_CHAT_UPDATE = 29,
    /**
     * 团队设置读取权限
     *
     * @generated from protobuf enum value: PERMISSION_TEAM_SETTINGS_READ = 30;
     */
    TEAM_SETTINGS_READ = 30,
    /**
     * 团队设置更新权限
     *
     * @generated from protobuf enum value: PERMISSION_TEAM_SETTINGS_UPDATE = 31;
     */
    TEAM_SETTINGS_UPDATE = 31
}
/**
 * 模型定价类型枚举 - update 2025-06-21 windsurf 1.11.0
 *
 * @generated from protobuf enum exa.seat_management_pb.ModelPricingType
 */
export enum ModelPricingType {
    /**
     * 未指定定价类型
     *
     * @generated from protobuf enum value: MODEL_PRICING_TYPE_UNSPECIFIED = 0;
     */
    UNSPECIFIED = 0,
    /**
     * 静态点数
     *
     * @generated from protobuf enum value: MODEL_PRICING_TYPE_STATIC_CREDIT = 1;
     */
    STATIC_CREDIT = 1,
    /**
     * API 定价
     *
     * @generated from protobuf enum value: MODEL_PRICING_TYPE_API = 2;
     */
    API = 2,
    /**
     * 自带密钥 (Bring Your Own Key)
     *
     * @generated from protobuf enum value: MODEL_PRICING_TYPE_BYOK = 3;
     */
    BYOK = 3
}
/**
 * 提供商枚举 - update 2025-06-21 windsurf 1.11.0
 *
 * @generated from protobuf enum exa.seat_management_pb.Provider
 */
export enum Provider {
    /**
     * 未指定提供商
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_UNSPECIFIED = 0;
     */
    MODEL_PROVIDER_UNSPECIFIED = 0,
    /**
     * Anthropic
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_ANTHROPIC = 1;
     */
    MODEL_PROVIDER_ANTHROPIC = 1,
    /**
     * OpenAI
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_OPENAI = 2;
     */
    MODEL_PROVIDER_OPENAI = 2,
    /**
     * Google
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_GOOGLE = 3;
     */
    MODEL_PROVIDER_GOOGLE = 3,
    /**
     * DeepSeek
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_DEEPSEEK = 4;
     */
    MODEL_PROVIDER_DEEPSEEK = 4,
    /**
     * 新增提供商 - update 2025-06-21 windsurf 1.12.1
     *
     * Qwen
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_QWEN = 5;
     */
    MODEL_PROVIDER_QWEN = 5,
    /**
     * Kimi
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_KIMI = 6;
     */
    MODEL_PROVIDER_KIMI = 6
}
// @generated message type with reflection information, may provide speed optimized methods
class GetUserStatusResponse$Type extends MessageType<GetUserStatusResponse> {
    constructor() {
        super("exa.seat_management_pb.GetUserStatusResponse", [
            { no: 1, name: "user_status", kind: "message", T: () => UserStatus },
            { no: 2, name: "plan_info", kind: "message", T: () => PlanInfo }
        ]);
    }
    create(value?: PartialMessage<GetUserStatusResponse>): GetUserStatusResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<GetUserStatusResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetUserStatusResponse): GetUserStatusResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* exa.seat_management_pb.UserStatus user_status */ 1:
                    message.userStatus = UserStatus.internalBinaryRead(reader, reader.uint32(), options, message.userStatus);
                    break;
                case /* exa.seat_management_pb.PlanInfo plan_info */ 2:
                    message.planInfo = PlanInfo.internalBinaryRead(reader, reader.uint32(), options, message.planInfo);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetUserStatusResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* exa.seat_management_pb.UserStatus user_status = 1; */
        if (message.userStatus)
            UserStatus.internalBinaryWrite(message.userStatus, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* exa.seat_management_pb.PlanInfo plan_info = 2; */
        if (message.planInfo)
            PlanInfo.internalBinaryWrite(message.planInfo, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.seat_management_pb.GetUserStatusResponse
 */
export const GetUserStatusResponse = new GetUserStatusResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class UserStatus$Type extends MessageType<UserStatus> {
    constructor() {
        super("exa.seat_management_pb.UserStatus", [
            { no: 1, name: "pro", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 2, name: "disable_telemetry", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 3, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "ignore_chat_telemetry_setting", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 5, name: "team_id", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 6, name: "team_status", kind: "enum", T: () => ["exa.seat_management_pb.UserTeamStatus", UserTeamStatus, "USER_TEAM_STATUS_"] },
            { no: 7, name: "email", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 8, name: "teams_features", kind: "enum", repeat: 1 /*RepeatType.PACKED*/, T: () => ["exa.seat_management_pb.TeamsFeatures", TeamsFeatures, "TEAMS_FEATURES_"] },
            { no: 9, name: "user_features", kind: "enum", repeat: 1 /*RepeatType.PACKED*/, T: () => ["exa.seat_management_pb.UserFeatures", UserFeatures, "USER_FEATURES_"] },
            { no: 11, name: "permissions", kind: "enum", repeat: 1 /*RepeatType.PACKED*/, T: () => ["exa.seat_management_pb.Permission", Permission, "PERMISSION_"] },
            { no: 12, name: "plan_info", kind: "message", T: () => PlanInfo },
            { no: 13, name: "plan_status", kind: "message", T: () => PlanStatus },
            { no: 28, name: "user_used_prompt_credits", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 29, name: "user_used_flow_credits", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 30, name: "has_fingerprint_set", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 31, name: "has_used_windsurf", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 32, name: "team_config", kind: "message", T: () => TeamConfig },
            { no: 33, name: "cascade_model_config_data", kind: "message", T: () => CascadeModelConfigData }
        ]);
    }
    create(value?: PartialMessage<UserStatus>): UserStatus {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.pro = false;
        message.disableTelemetry = false;
        message.name = "";
        message.ignoreChatTelemetrySetting = false;
        message.teamId = "";
        message.teamStatus = 0;
        message.email = "";
        message.teamsFeatures = [];
        message.userFeatures = [];
        message.permissions = [];
        message.userUsedPromptCredits = 0n;
        message.userUsedFlowCredits = 0n;
        message.hasFingerprintSet = false;
        message.hasUsedWindsurf = false;
        if (value !== undefined)
            reflectionMergePartial<UserStatus>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: UserStatus): UserStatus {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* bool pro */ 1:
                    message.pro = reader.bool();
                    break;
                case /* bool disable_telemetry */ 2:
                    message.disableTelemetry = reader.bool();
                    break;
                case /* string name */ 3:
                    message.name = reader.string();
                    break;
                case /* bool ignore_chat_telemetry_setting */ 4:
                    message.ignoreChatTelemetrySetting = reader.bool();
                    break;
                case /* string team_id */ 5:
                    message.teamId = reader.string();
                    break;
                case /* exa.seat_management_pb.UserTeamStatus team_status */ 6:
                    message.teamStatus = reader.int32();
                    break;
                case /* string email */ 7:
                    message.email = reader.string();
                    break;
                case /* repeated exa.seat_management_pb.TeamsFeatures teams_features */ 8:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.teamsFeatures.push(reader.int32());
                    else
                        message.teamsFeatures.push(reader.int32());
                    break;
                case /* repeated exa.seat_management_pb.UserFeatures user_features */ 9:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.userFeatures.push(reader.int32());
                    else
                        message.userFeatures.push(reader.int32());
                    break;
                case /* repeated exa.seat_management_pb.Permission permissions */ 11:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.permissions.push(reader.int32());
                    else
                        message.permissions.push(reader.int32());
                    break;
                case /* exa.seat_management_pb.PlanInfo plan_info */ 12:
                    message.planInfo = PlanInfo.internalBinaryRead(reader, reader.uint32(), options, message.planInfo);
                    break;
                case /* exa.seat_management_pb.PlanStatus plan_status */ 13:
                    message.planStatus = PlanStatus.internalBinaryRead(reader, reader.uint32(), options, message.planStatus);
                    break;
                case /* int64 user_used_prompt_credits */ 28:
                    message.userUsedPromptCredits = reader.int64().toBigInt();
                    break;
                case /* int64 user_used_flow_credits */ 29:
                    message.userUsedFlowCredits = reader.int64().toBigInt();
                    break;
                case /* bool has_fingerprint_set */ 30:
                    message.hasFingerprintSet = reader.bool();
                    break;
                case /* bool has_used_windsurf */ 31:
                    message.hasUsedWindsurf = reader.bool();
                    break;
                case /* exa.seat_management_pb.TeamConfig team_config */ 32:
                    message.teamConfig = TeamConfig.internalBinaryRead(reader, reader.uint32(), options, message.teamConfig);
                    break;
                case /* exa.seat_management_pb.CascadeModelConfigData cascade_model_config_data */ 33:
                    message.cascadeModelConfigData = CascadeModelConfigData.internalBinaryRead(reader, reader.uint32(), options, message.cascadeModelConfigData);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: UserStatus, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* bool pro = 1; */
        if (message.pro !== false)
            writer.tag(1, WireType.Varint).bool(message.pro);
        /* bool disable_telemetry = 2; */
        if (message.disableTelemetry !== false)
            writer.tag(2, WireType.Varint).bool(message.disableTelemetry);
        /* string name = 3; */
        if (message.name !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.name);
        /* bool ignore_chat_telemetry_setting = 4; */
        if (message.ignoreChatTelemetrySetting !== false)
            writer.tag(4, WireType.Varint).bool(message.ignoreChatTelemetrySetting);
        /* string team_id = 5; */
        if (message.teamId !== "")
            writer.tag(5, WireType.LengthDelimited).string(message.teamId);
        /* exa.seat_management_pb.UserTeamStatus team_status = 6; */
        if (message.teamStatus !== 0)
            writer.tag(6, WireType.Varint).int32(message.teamStatus);
        /* string email = 7; */
        if (message.email !== "")
            writer.tag(7, WireType.LengthDelimited).string(message.email);
        /* repeated exa.seat_management_pb.TeamsFeatures teams_features = 8; */
        if (message.teamsFeatures.length) {
            writer.tag(8, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.teamsFeatures.length; i++)
                writer.int32(message.teamsFeatures[i]);
            writer.join();
        }
        /* repeated exa.seat_management_pb.UserFeatures user_features = 9; */
        if (message.userFeatures.length) {
            writer.tag(9, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.userFeatures.length; i++)
                writer.int32(message.userFeatures[i]);
            writer.join();
        }
        /* repeated exa.seat_management_pb.Permission permissions = 11; */
        if (message.permissions.length) {
            writer.tag(11, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.permissions.length; i++)
                writer.int32(message.permissions[i]);
            writer.join();
        }
        /* exa.seat_management_pb.PlanInfo plan_info = 12; */
        if (message.planInfo)
            PlanInfo.internalBinaryWrite(message.planInfo, writer.tag(12, WireType.LengthDelimited).fork(), options).join();
        /* exa.seat_management_pb.PlanStatus plan_status = 13; */
        if (message.planStatus)
            PlanStatus.internalBinaryWrite(message.planStatus, writer.tag(13, WireType.LengthDelimited).fork(), options).join();
        /* int64 user_used_prompt_credits = 28; */
        if (message.userUsedPromptCredits !== 0n)
            writer.tag(28, WireType.Varint).int64(message.userUsedPromptCredits);
        /* int64 user_used_flow_credits = 29; */
        if (message.userUsedFlowCredits !== 0n)
            writer.tag(29, WireType.Varint).int64(message.userUsedFlowCredits);
        /* bool has_fingerprint_set = 30; */
        if (message.hasFingerprintSet !== false)
            writer.tag(30, WireType.Varint).bool(message.hasFingerprintSet);
        /* bool has_used_windsurf = 31; */
        if (message.hasUsedWindsurf !== false)
            writer.tag(31, WireType.Varint).bool(message.hasUsedWindsurf);
        /* exa.seat_management_pb.TeamConfig team_config = 32; */
        if (message.teamConfig)
            TeamConfig.internalBinaryWrite(message.teamConfig, writer.tag(32, WireType.LengthDelimited).fork(), options).join();
        /* exa.seat_management_pb.CascadeModelConfigData cascade_model_config_data = 33; */
        if (message.cascadeModelConfigData)
            CascadeModelConfigData.internalBinaryWrite(message.cascadeModelConfigData, writer.tag(33, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.seat_management_pb.UserStatus
 */
export const UserStatus = new UserStatus$Type();
// @generated message type with reflection information, may provide speed optimized methods
class PlanStatus$Type extends MessageType<PlanStatus> {
    constructor() {
        super("exa.seat_management_pb.PlanStatus", [
            { no: 1, name: "plan_info", kind: "message", T: () => PlanInfo },
            { no: 2, name: "plan_start", kind: "message", T: () => Timestamp },
            { no: 3, name: "plan_end", kind: "message", T: () => Timestamp },
            { no: 4, name: "available_flex_credits", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 5, name: "used_flow_credits", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 6, name: "used_prompt_credits", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 7, name: "used_flex_credits", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 8, name: "available_prompt_credits", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 9, name: "available_flow_credits", kind: "scalar", T: 5 /*ScalarType.INT32*/ }
        ]);
    }
    create(value?: PartialMessage<PlanStatus>): PlanStatus {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.availableFlexCredits = 0;
        message.usedFlowCredits = 0;
        message.usedPromptCredits = 0;
        message.usedFlexCredits = 0;
        message.availablePromptCredits = 0;
        message.availableFlowCredits = 0;
        if (value !== undefined)
            reflectionMergePartial<PlanStatus>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: PlanStatus): PlanStatus {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* exa.seat_management_pb.PlanInfo plan_info */ 1:
                    message.planInfo = PlanInfo.internalBinaryRead(reader, reader.uint32(), options, message.planInfo);
                    break;
                case /* google.protobuf.Timestamp plan_start */ 2:
                    message.planStart = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.planStart);
                    break;
                case /* google.protobuf.Timestamp plan_end */ 3:
                    message.planEnd = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.planEnd);
                    break;
                case /* int32 available_flex_credits */ 4:
                    message.availableFlexCredits = reader.int32();
                    break;
                case /* int32 used_flow_credits */ 5:
                    message.usedFlowCredits = reader.int32();
                    break;
                case /* int32 used_prompt_credits */ 6:
                    message.usedPromptCredits = reader.int32();
                    break;
                case /* int32 used_flex_credits */ 7:
                    message.usedFlexCredits = reader.int32();
                    break;
                case /* int32 available_prompt_credits */ 8:
                    message.availablePromptCredits = reader.int32();
                    break;
                case /* int32 available_flow_credits */ 9:
                    message.availableFlowCredits = reader.int32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: PlanStatus, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* exa.seat_management_pb.PlanInfo plan_info = 1; */
        if (message.planInfo)
            PlanInfo.internalBinaryWrite(message.planInfo, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* google.protobuf.Timestamp plan_start = 2; */
        if (message.planStart)
            Timestamp.internalBinaryWrite(message.planStart, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* google.protobuf.Timestamp plan_end = 3; */
        if (message.planEnd)
            Timestamp.internalBinaryWrite(message.planEnd, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        /* int32 available_flex_credits = 4; */
        if (message.availableFlexCredits !== 0)
            writer.tag(4, WireType.Varint).int32(message.availableFlexCredits);
        /* int32 used_flow_credits = 5; */
        if (message.usedFlowCredits !== 0)
            writer.tag(5, WireType.Varint).int32(message.usedFlowCredits);
        /* int32 used_prompt_credits = 6; */
        if (message.usedPromptCredits !== 0)
            writer.tag(6, WireType.Varint).int32(message.usedPromptCredits);
        /* int32 used_flex_credits = 7; */
        if (message.usedFlexCredits !== 0)
            writer.tag(7, WireType.Varint).int32(message.usedFlexCredits);
        /* int32 available_prompt_credits = 8; */
        if (message.availablePromptCredits !== 0)
            writer.tag(8, WireType.Varint).int32(message.availablePromptCredits);
        /* int32 available_flow_credits = 9; */
        if (message.availableFlowCredits !== 0)
            writer.tag(9, WireType.Varint).int32(message.availableFlowCredits);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.seat_management_pb.PlanStatus
 */
export const PlanStatus = new PlanStatus$Type();
// @generated message type with reflection information, may provide speed optimized methods
class PlanInfo$Type extends MessageType<PlanInfo> {
    constructor() {
        super("exa.seat_management_pb.PlanInfo", [
            { no: 1, name: "teams_tier", kind: "enum", T: () => ["exa.seat_management_pb.TeamsTier", TeamsTier, "TEAMS_TIER_"] },
            { no: 2, name: "plan_name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "has_autocomplete_fast_mode", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 4, name: "allow_sticky_premium_models", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 5, name: "has_forge_access", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 6, name: "max_num_premium_chat_messages", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 7, name: "max_num_chat_input_tokens", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 8, name: "max_custom_chat_instruction_characters", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 9, name: "max_num_pinned_context_items", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 10, name: "max_local_index_size", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 11, name: "disable_code_snippet_telemetry", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 12, name: "monthly_prompt_credits", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 13, name: "monthly_flow_credits", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 14, name: "monthly_flex_credit_purchase_amount", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 15, name: "allow_premium_command_models", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 16, name: "is_enterprise", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 17, name: "is_teams", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 18, name: "can_buy_more_credits", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 19, name: "cascade_web_search_enabled", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 20, name: "can_customize_app_icon", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 21, name: "cascade_allowed_models_config", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => AllowedModelConfig },
            { no: 22, name: "cascade_can_auto_run_commands", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 23, name: "has_tab_to_jump", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 24, name: "default_team_config", kind: "message", T: () => TeamConfig },
            { no: 25, name: "can_generate_commit_messages", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 26, name: "max_unclaimed_sites", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 27, name: "knowledge_base_enabled", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 28, name: "can_share_conversations", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 29, name: "can_allow_cascade_in_background", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 30, name: "default_team_features", kind: "map", K: 5 /*ScalarType.INT32*/, V: { kind: "message", T: () => TeamFeatureConfig } },
            { no: 31, name: "browser_enabled", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
    create(value?: PartialMessage<PlanInfo>): PlanInfo {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.teamsTier = 0;
        message.planName = "";
        message.hasAutocompleteFastMode = false;
        message.allowStickyPremiumModels = false;
        message.hasForgeAccess = false;
        message.maxNumPremiumChatMessages = 0n;
        message.maxNumChatInputTokens = 0n;
        message.maxCustomChatInstructionCharacters = 0n;
        message.maxNumPinnedContextItems = 0n;
        message.maxLocalIndexSize = 0n;
        message.disableCodeSnippetTelemetry = false;
        message.monthlyPromptCredits = 0;
        message.monthlyFlowCredits = 0;
        message.monthlyFlexCreditPurchaseAmount = 0;
        message.allowPremiumCommandModels = false;
        message.isEnterprise = false;
        message.isTeams = false;
        message.canBuyMoreCredits = false;
        message.cascadeWebSearchEnabled = false;
        message.canCustomizeAppIcon = false;
        message.cascadeAllowedModelsConfig = [];
        message.cascadeCanAutoRunCommands = false;
        message.hasTabToJump = false;
        message.canGenerateCommitMessages = false;
        message.maxUnclaimedSites = 0;
        message.knowledgeBaseEnabled = false;
        message.canShareConversations = false;
        message.canAllowCascadeInBackground = false;
        message.defaultTeamFeatures = {};
        message.browserEnabled = false;
        if (value !== undefined)
            reflectionMergePartial<PlanInfo>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: PlanInfo): PlanInfo {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* exa.seat_management_pb.TeamsTier teams_tier */ 1:
                    message.teamsTier = reader.int32();
                    break;
                case /* string plan_name */ 2:
                    message.planName = reader.string();
                    break;
                case /* bool has_autocomplete_fast_mode */ 3:
                    message.hasAutocompleteFastMode = reader.bool();
                    break;
                case /* bool allow_sticky_premium_models */ 4:
                    message.allowStickyPremiumModels = reader.bool();
                    break;
                case /* bool has_forge_access */ 5:
                    message.hasForgeAccess = reader.bool();
                    break;
                case /* int64 max_num_premium_chat_messages */ 6:
                    message.maxNumPremiumChatMessages = reader.int64().toBigInt();
                    break;
                case /* int64 max_num_chat_input_tokens */ 7:
                    message.maxNumChatInputTokens = reader.int64().toBigInt();
                    break;
                case /* int64 max_custom_chat_instruction_characters */ 8:
                    message.maxCustomChatInstructionCharacters = reader.int64().toBigInt();
                    break;
                case /* int64 max_num_pinned_context_items */ 9:
                    message.maxNumPinnedContextItems = reader.int64().toBigInt();
                    break;
                case /* int64 max_local_index_size */ 10:
                    message.maxLocalIndexSize = reader.int64().toBigInt();
                    break;
                case /* bool disable_code_snippet_telemetry */ 11:
                    message.disableCodeSnippetTelemetry = reader.bool();
                    break;
                case /* int32 monthly_prompt_credits */ 12:
                    message.monthlyPromptCredits = reader.int32();
                    break;
                case /* int32 monthly_flow_credits */ 13:
                    message.monthlyFlowCredits = reader.int32();
                    break;
                case /* int32 monthly_flex_credit_purchase_amount */ 14:
                    message.monthlyFlexCreditPurchaseAmount = reader.int32();
                    break;
                case /* bool allow_premium_command_models */ 15:
                    message.allowPremiumCommandModels = reader.bool();
                    break;
                case /* bool is_enterprise */ 16:
                    message.isEnterprise = reader.bool();
                    break;
                case /* bool is_teams */ 17:
                    message.isTeams = reader.bool();
                    break;
                case /* bool can_buy_more_credits */ 18:
                    message.canBuyMoreCredits = reader.bool();
                    break;
                case /* bool cascade_web_search_enabled */ 19:
                    message.cascadeWebSearchEnabled = reader.bool();
                    break;
                case /* bool can_customize_app_icon */ 20:
                    message.canCustomizeAppIcon = reader.bool();
                    break;
                case /* repeated exa.seat_management_pb.AllowedModelConfig cascade_allowed_models_config */ 21:
                    message.cascadeAllowedModelsConfig.push(AllowedModelConfig.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* bool cascade_can_auto_run_commands */ 22:
                    message.cascadeCanAutoRunCommands = reader.bool();
                    break;
                case /* bool has_tab_to_jump */ 23:
                    message.hasTabToJump = reader.bool();
                    break;
                case /* exa.seat_management_pb.TeamConfig default_team_config */ 24:
                    message.defaultTeamConfig = TeamConfig.internalBinaryRead(reader, reader.uint32(), options, message.defaultTeamConfig);
                    break;
                case /* bool can_generate_commit_messages */ 25:
                    message.canGenerateCommitMessages = reader.bool();
                    break;
                case /* int32 max_unclaimed_sites */ 26:
                    message.maxUnclaimedSites = reader.int32();
                    break;
                case /* bool knowledge_base_enabled */ 27:
                    message.knowledgeBaseEnabled = reader.bool();
                    break;
                case /* bool can_share_conversations */ 28:
                    message.canShareConversations = reader.bool();
                    break;
                case /* bool can_allow_cascade_in_background */ 29:
                    message.canAllowCascadeInBackground = reader.bool();
                    break;
                case /* map<int32, exa.seat_management_pb.TeamFeatureConfig> default_team_features */ 30:
                    this.binaryReadMap30(message.defaultTeamFeatures, reader, options);
                    break;
                case /* bool browser_enabled */ 31:
                    message.browserEnabled = reader.bool();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    private binaryReadMap30(map: PlanInfo["defaultTeamFeatures"], reader: IBinaryReader, options: BinaryReadOptions): void {
        let len = reader.uint32(), end = reader.pos + len, key: keyof PlanInfo["defaultTeamFeatures"] | undefined, val: PlanInfo["defaultTeamFeatures"][any] | undefined;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case 1:
                    key = reader.int32();
                    break;
                case 2:
                    val = TeamFeatureConfig.internalBinaryRead(reader, reader.uint32(), options);
                    break;
                default: throw new globalThis.Error("unknown map entry field for field exa.seat_management_pb.PlanInfo.default_team_features");
            }
        }
        map[key ?? 0] = val ?? TeamFeatureConfig.create();
    }
    internalBinaryWrite(message: PlanInfo, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* exa.seat_management_pb.TeamsTier teams_tier = 1; */
        if (message.teamsTier !== 0)
            writer.tag(1, WireType.Varint).int32(message.teamsTier);
        /* string plan_name = 2; */
        if (message.planName !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.planName);
        /* bool has_autocomplete_fast_mode = 3; */
        if (message.hasAutocompleteFastMode !== false)
            writer.tag(3, WireType.Varint).bool(message.hasAutocompleteFastMode);
        /* bool allow_sticky_premium_models = 4; */
        if (message.allowStickyPremiumModels !== false)
            writer.tag(4, WireType.Varint).bool(message.allowStickyPremiumModels);
        /* bool has_forge_access = 5; */
        if (message.hasForgeAccess !== false)
            writer.tag(5, WireType.Varint).bool(message.hasForgeAccess);
        /* int64 max_num_premium_chat_messages = 6; */
        if (message.maxNumPremiumChatMessages !== 0n)
            writer.tag(6, WireType.Varint).int64(message.maxNumPremiumChatMessages);
        /* int64 max_num_chat_input_tokens = 7; */
        if (message.maxNumChatInputTokens !== 0n)
            writer.tag(7, WireType.Varint).int64(message.maxNumChatInputTokens);
        /* int64 max_custom_chat_instruction_characters = 8; */
        if (message.maxCustomChatInstructionCharacters !== 0n)
            writer.tag(8, WireType.Varint).int64(message.maxCustomChatInstructionCharacters);
        /* int64 max_num_pinned_context_items = 9; */
        if (message.maxNumPinnedContextItems !== 0n)
            writer.tag(9, WireType.Varint).int64(message.maxNumPinnedContextItems);
        /* int64 max_local_index_size = 10; */
        if (message.maxLocalIndexSize !== 0n)
            writer.tag(10, WireType.Varint).int64(message.maxLocalIndexSize);
        /* bool disable_code_snippet_telemetry = 11; */
        if (message.disableCodeSnippetTelemetry !== false)
            writer.tag(11, WireType.Varint).bool(message.disableCodeSnippetTelemetry);
        /* int32 monthly_prompt_credits = 12; */
        if (message.monthlyPromptCredits !== 0)
            writer.tag(12, WireType.Varint).int32(message.monthlyPromptCredits);
        /* int32 monthly_flow_credits = 13; */
        if (message.monthlyFlowCredits !== 0)
            writer.tag(13, WireType.Varint).int32(message.monthlyFlowCredits);
        /* int32 monthly_flex_credit_purchase_amount = 14; */
        if (message.monthlyFlexCreditPurchaseAmount !== 0)
            writer.tag(14, WireType.Varint).int32(message.monthlyFlexCreditPurchaseAmount);
        /* bool allow_premium_command_models = 15; */
        if (message.allowPremiumCommandModels !== false)
            writer.tag(15, WireType.Varint).bool(message.allowPremiumCommandModels);
        /* bool is_enterprise = 16; */
        if (message.isEnterprise !== false)
            writer.tag(16, WireType.Varint).bool(message.isEnterprise);
        /* bool is_teams = 17; */
        if (message.isTeams !== false)
            writer.tag(17, WireType.Varint).bool(message.isTeams);
        /* bool can_buy_more_credits = 18; */
        if (message.canBuyMoreCredits !== false)
            writer.tag(18, WireType.Varint).bool(message.canBuyMoreCredits);
        /* bool cascade_web_search_enabled = 19; */
        if (message.cascadeWebSearchEnabled !== false)
            writer.tag(19, WireType.Varint).bool(message.cascadeWebSearchEnabled);
        /* bool can_customize_app_icon = 20; */
        if (message.canCustomizeAppIcon !== false)
            writer.tag(20, WireType.Varint).bool(message.canCustomizeAppIcon);
        /* repeated exa.seat_management_pb.AllowedModelConfig cascade_allowed_models_config = 21; */
        for (let i = 0; i < message.cascadeAllowedModelsConfig.length; i++)
            AllowedModelConfig.internalBinaryWrite(message.cascadeAllowedModelsConfig[i], writer.tag(21, WireType.LengthDelimited).fork(), options).join();
        /* bool cascade_can_auto_run_commands = 22; */
        if (message.cascadeCanAutoRunCommands !== false)
            writer.tag(22, WireType.Varint).bool(message.cascadeCanAutoRunCommands);
        /* bool has_tab_to_jump = 23; */
        if (message.hasTabToJump !== false)
            writer.tag(23, WireType.Varint).bool(message.hasTabToJump);
        /* exa.seat_management_pb.TeamConfig default_team_config = 24; */
        if (message.defaultTeamConfig)
            TeamConfig.internalBinaryWrite(message.defaultTeamConfig, writer.tag(24, WireType.LengthDelimited).fork(), options).join();
        /* bool can_generate_commit_messages = 25; */
        if (message.canGenerateCommitMessages !== false)
            writer.tag(25, WireType.Varint).bool(message.canGenerateCommitMessages);
        /* int32 max_unclaimed_sites = 26; */
        if (message.maxUnclaimedSites !== 0)
            writer.tag(26, WireType.Varint).int32(message.maxUnclaimedSites);
        /* bool knowledge_base_enabled = 27; */
        if (message.knowledgeBaseEnabled !== false)
            writer.tag(27, WireType.Varint).bool(message.knowledgeBaseEnabled);
        /* bool can_share_conversations = 28; */
        if (message.canShareConversations !== false)
            writer.tag(28, WireType.Varint).bool(message.canShareConversations);
        /* bool can_allow_cascade_in_background = 29; */
        if (message.canAllowCascadeInBackground !== false)
            writer.tag(29, WireType.Varint).bool(message.canAllowCascadeInBackground);
        /* map<int32, exa.seat_management_pb.TeamFeatureConfig> default_team_features = 30; */
        for (let k of globalThis.Object.keys(message.defaultTeamFeatures)) {
            writer.tag(30, WireType.LengthDelimited).fork().tag(1, WireType.Varint).int32(parseInt(k));
            writer.tag(2, WireType.LengthDelimited).fork();
            TeamFeatureConfig.internalBinaryWrite(message.defaultTeamFeatures[k as any], writer, options);
            writer.join().join();
        }
        /* bool browser_enabled = 31; */
        if (message.browserEnabled !== false)
            writer.tag(31, WireType.Varint).bool(message.browserEnabled);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.seat_management_pb.PlanInfo
 */
export const PlanInfo = new PlanInfo$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TeamFeatureConfig$Type extends MessageType<TeamFeatureConfig> {
    constructor() {
        super("exa.seat_management_pb.TeamFeatureConfig", [
            { no: 1, name: "enabled", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 2, name: "config_params", kind: "map", K: 9 /*ScalarType.STRING*/, V: { kind: "scalar", T: 9 /*ScalarType.STRING*/ } },
            { no: 3, name: "usage_limit", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "description", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<TeamFeatureConfig>): TeamFeatureConfig {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.enabled = false;
        message.configParams = {};
        message.usageLimit = 0n;
        message.description = "";
        if (value !== undefined)
            reflectionMergePartial<TeamFeatureConfig>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TeamFeatureConfig): TeamFeatureConfig {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* bool enabled */ 1:
                    message.enabled = reader.bool();
                    break;
                case /* map<string, string> config_params */ 2:
                    this.binaryReadMap2(message.configParams, reader, options);
                    break;
                case /* int64 usage_limit */ 3:
                    message.usageLimit = reader.int64().toBigInt();
                    break;
                case /* string description */ 4:
                    message.description = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    private binaryReadMap2(map: TeamFeatureConfig["configParams"], reader: IBinaryReader, options: BinaryReadOptions): void {
        let len = reader.uint32(), end = reader.pos + len, key: keyof TeamFeatureConfig["configParams"] | undefined, val: TeamFeatureConfig["configParams"][any] | undefined;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case 1:
                    key = reader.string();
                    break;
                case 2:
                    val = reader.string();
                    break;
                default: throw new globalThis.Error("unknown map entry field for field exa.seat_management_pb.TeamFeatureConfig.config_params");
            }
        }
        map[key ?? ""] = val ?? "";
    }
    internalBinaryWrite(message: TeamFeatureConfig, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* bool enabled = 1; */
        if (message.enabled !== false)
            writer.tag(1, WireType.Varint).bool(message.enabled);
        /* map<string, string> config_params = 2; */
        for (let k of globalThis.Object.keys(message.configParams))
            writer.tag(2, WireType.LengthDelimited).fork().tag(1, WireType.LengthDelimited).string(k).tag(2, WireType.LengthDelimited).string(message.configParams[k]).join();
        /* int64 usage_limit = 3; */
        if (message.usageLimit !== 0n)
            writer.tag(3, WireType.Varint).int64(message.usageLimit);
        /* string description = 4; */
        if (message.description !== "")
            writer.tag(4, WireType.LengthDelimited).string(message.description);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.seat_management_pb.TeamFeatureConfig
 */
export const TeamFeatureConfig = new TeamFeatureConfig$Type();
// @generated message type with reflection information, may provide speed optimized methods
class CascadeModelConfigData$Type extends MessageType<CascadeModelConfigData> {
    constructor() {
        super("exa.seat_management_pb.CascadeModelConfigData", [
            { no: 1, name: "client_model_configs", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ClientModelConfig },
            { no: 2, name: "client_model_sorts", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ClientModelSort },
            { no: 3, name: "default_override_model_config", kind: "message", T: () => DefaultOverrideModelConfig }
        ]);
    }
    create(value?: PartialMessage<CascadeModelConfigData>): CascadeModelConfigData {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.clientModelConfigs = [];
        message.clientModelSorts = [];
        if (value !== undefined)
            reflectionMergePartial<CascadeModelConfigData>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: CascadeModelConfigData): CascadeModelConfigData {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated exa.seat_management_pb.ClientModelConfig client_model_configs */ 1:
                    message.clientModelConfigs.push(ClientModelConfig.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* repeated exa.seat_management_pb.ClientModelSort client_model_sorts */ 2:
                    message.clientModelSorts.push(ClientModelSort.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* exa.seat_management_pb.DefaultOverrideModelConfig default_override_model_config */ 3:
                    message.defaultOverrideModelConfig = DefaultOverrideModelConfig.internalBinaryRead(reader, reader.uint32(), options, message.defaultOverrideModelConfig);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: CascadeModelConfigData, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated exa.seat_management_pb.ClientModelConfig client_model_configs = 1; */
        for (let i = 0; i < message.clientModelConfigs.length; i++)
            ClientModelConfig.internalBinaryWrite(message.clientModelConfigs[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* repeated exa.seat_management_pb.ClientModelSort client_model_sorts = 2; */
        for (let i = 0; i < message.clientModelSorts.length; i++)
            ClientModelSort.internalBinaryWrite(message.clientModelSorts[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* exa.seat_management_pb.DefaultOverrideModelConfig default_override_model_config = 3; */
        if (message.defaultOverrideModelConfig)
            DefaultOverrideModelConfig.internalBinaryWrite(message.defaultOverrideModelConfig, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.seat_management_pb.CascadeModelConfigData
 */
export const CascadeModelConfigData = new CascadeModelConfigData$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ClientModelGroup$Type extends MessageType<ClientModelGroup> {
    constructor() {
        super("exa.seat_management_pb.ClientModelGroup", [
            { no: 1, name: "group_name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "model_labels", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<ClientModelGroup>): ClientModelGroup {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.groupName = "";
        message.modelLabels = [];
        if (value !== undefined)
            reflectionMergePartial<ClientModelGroup>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ClientModelGroup): ClientModelGroup {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string group_name */ 1:
                    message.groupName = reader.string();
                    break;
                case /* repeated string model_labels */ 2:
                    message.modelLabels.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ClientModelGroup, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string group_name = 1; */
        if (message.groupName !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.groupName);
        /* repeated string model_labels = 2; */
        for (let i = 0; i < message.modelLabels.length; i++)
            writer.tag(2, WireType.LengthDelimited).string(message.modelLabels[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.seat_management_pb.ClientModelGroup
 */
export const ClientModelGroup = new ClientModelGroup$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ClientModelSort$Type extends MessageType<ClientModelSort> {
    constructor() {
        super("exa.seat_management_pb.ClientModelSort", [
            { no: 1, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "groups", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ClientModelGroup }
        ]);
    }
    create(value?: PartialMessage<ClientModelSort>): ClientModelSort {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.name = "";
        message.groups = [];
        if (value !== undefined)
            reflectionMergePartial<ClientModelSort>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ClientModelSort): ClientModelSort {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string name */ 1:
                    message.name = reader.string();
                    break;
                case /* repeated exa.seat_management_pb.ClientModelGroup groups */ 2:
                    message.groups.push(ClientModelGroup.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ClientModelSort, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string name = 1; */
        if (message.name !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.name);
        /* repeated exa.seat_management_pb.ClientModelGroup groups = 2; */
        for (let i = 0; i < message.groups.length; i++)
            ClientModelGroup.internalBinaryWrite(message.groups[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.seat_management_pb.ClientModelSort
 */
export const ClientModelSort = new ClientModelSort$Type();
// @generated message type with reflection information, may provide speed optimized methods
class DefaultOverrideModelConfig$Type extends MessageType<DefaultOverrideModelConfig> {
    constructor() {
        super("exa.seat_management_pb.DefaultOverrideModelConfig", [
            { no: 1, name: "model_or_alias", kind: "message", T: () => ModelOrAlias },
            { no: 2, name: "version_id", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<DefaultOverrideModelConfig>): DefaultOverrideModelConfig {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.versionId = "";
        if (value !== undefined)
            reflectionMergePartial<DefaultOverrideModelConfig>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: DefaultOverrideModelConfig): DefaultOverrideModelConfig {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* exa.seat_management_pb.ModelOrAlias model_or_alias */ 1:
                    message.modelOrAlias = ModelOrAlias.internalBinaryRead(reader, reader.uint32(), options, message.modelOrAlias);
                    break;
                case /* string version_id */ 2:
                    message.versionId = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: DefaultOverrideModelConfig, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* exa.seat_management_pb.ModelOrAlias model_or_alias = 1; */
        if (message.modelOrAlias)
            ModelOrAlias.internalBinaryWrite(message.modelOrAlias, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* string version_id = 2; */
        if (message.versionId !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.versionId);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.seat_management_pb.DefaultOverrideModelConfig
 */
export const DefaultOverrideModelConfig = new DefaultOverrideModelConfig$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TeamConfig$Type extends MessageType<TeamConfig> {
    constructor() {
        super("exa.seat_management_pb.TeamConfig", [
            { no: 1, name: "team_id", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "user_prompt_credit_cap", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "user_flow_credit_cap", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "auto_provision_cascade_seat", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 5, name: "allow_mcp_servers", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 7, name: "allow_auto_run_commands", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 8, name: "allow_custom_recipes", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 9, name: "max_unclaimed_sites", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 10, name: "allow_app_deployments", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 11, name: "max_new_sites_per_day", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<TeamConfig>): TeamConfig {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.teamId = "";
        message.userPromptCreditCap = 0n;
        message.userFlowCreditCap = 0n;
        message.autoProvisionCascadeSeat = false;
        message.allowMcpServers = false;
        message.allowAutoRunCommands = false;
        message.allowCustomRecipes = false;
        message.maxUnclaimedSites = 0n;
        message.allowAppDeployments = false;
        message.maxNewSitesPerDay = 0n;
        if (value !== undefined)
            reflectionMergePartial<TeamConfig>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TeamConfig): TeamConfig {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string team_id */ 1:
                    message.teamId = reader.string();
                    break;
                case /* int64 user_prompt_credit_cap */ 2:
                    message.userPromptCreditCap = reader.int64().toBigInt();
                    break;
                case /* int64 user_flow_credit_cap */ 3:
                    message.userFlowCreditCap = reader.int64().toBigInt();
                    break;
                case /* bool auto_provision_cascade_seat */ 4:
                    message.autoProvisionCascadeSeat = reader.bool();
                    break;
                case /* bool allow_mcp_servers */ 5:
                    message.allowMcpServers = reader.bool();
                    break;
                case /* bool allow_auto_run_commands */ 7:
                    message.allowAutoRunCommands = reader.bool();
                    break;
                case /* bool allow_custom_recipes */ 8:
                    message.allowCustomRecipes = reader.bool();
                    break;
                case /* int64 max_unclaimed_sites */ 9:
                    message.maxUnclaimedSites = reader.int64().toBigInt();
                    break;
                case /* bool allow_app_deployments */ 10:
                    message.allowAppDeployments = reader.bool();
                    break;
                case /* int64 max_new_sites_per_day */ 11:
                    message.maxNewSitesPerDay = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TeamConfig, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string team_id = 1; */
        if (message.teamId !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.teamId);
        /* int64 user_prompt_credit_cap = 2; */
        if (message.userPromptCreditCap !== 0n)
            writer.tag(2, WireType.Varint).int64(message.userPromptCreditCap);
        /* int64 user_flow_credit_cap = 3; */
        if (message.userFlowCreditCap !== 0n)
            writer.tag(3, WireType.Varint).int64(message.userFlowCreditCap);
        /* bool auto_provision_cascade_seat = 4; */
        if (message.autoProvisionCascadeSeat !== false)
            writer.tag(4, WireType.Varint).bool(message.autoProvisionCascadeSeat);
        /* bool allow_mcp_servers = 5; */
        if (message.allowMcpServers !== false)
            writer.tag(5, WireType.Varint).bool(message.allowMcpServers);
        /* bool allow_auto_run_commands = 7; */
        if (message.allowAutoRunCommands !== false)
            writer.tag(7, WireType.Varint).bool(message.allowAutoRunCommands);
        /* bool allow_custom_recipes = 8; */
        if (message.allowCustomRecipes !== false)
            writer.tag(8, WireType.Varint).bool(message.allowCustomRecipes);
        /* int64 max_unclaimed_sites = 9; */
        if (message.maxUnclaimedSites !== 0n)
            writer.tag(9, WireType.Varint).int64(message.maxUnclaimedSites);
        /* bool allow_app_deployments = 10; */
        if (message.allowAppDeployments !== false)
            writer.tag(10, WireType.Varint).bool(message.allowAppDeployments);
        /* int64 max_new_sites_per_day = 11; */
        if (message.maxNewSitesPerDay !== 0n)
            writer.tag(11, WireType.Varint).int64(message.maxNewSitesPerDay);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.seat_management_pb.TeamConfig
 */
export const TeamConfig = new TeamConfig$Type();
// @generated message type with reflection information, may provide speed optimized methods
class AllowedModelConfig$Type extends MessageType<AllowedModelConfig> {
    constructor() {
        super("exa.seat_management_pb.AllowedModelConfig", [
            { no: 1, name: "model_or_alias", kind: "message", T: () => ModelOrAlias },
            { no: 2, name: "credit_multiplier", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ }
        ]);
    }
    create(value?: PartialMessage<AllowedModelConfig>): AllowedModelConfig {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.creditMultiplier = 0;
        if (value !== undefined)
            reflectionMergePartial<AllowedModelConfig>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: AllowedModelConfig): AllowedModelConfig {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* exa.seat_management_pb.ModelOrAlias model_or_alias */ 1:
                    message.modelOrAlias = ModelOrAlias.internalBinaryRead(reader, reader.uint32(), options, message.modelOrAlias);
                    break;
                case /* float credit_multiplier */ 2:
                    message.creditMultiplier = reader.float();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: AllowedModelConfig, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* exa.seat_management_pb.ModelOrAlias model_or_alias = 1; */
        if (message.modelOrAlias)
            ModelOrAlias.internalBinaryWrite(message.modelOrAlias, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* float credit_multiplier = 2; */
        if (message.creditMultiplier !== 0)
            writer.tag(2, WireType.Bit32).float(message.creditMultiplier);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.seat_management_pb.AllowedModelConfig
 */
export const AllowedModelConfig = new AllowedModelConfig$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ModelOrAlias$Type extends MessageType<ModelOrAlias> {
    constructor() {
        super("exa.seat_management_pb.ModelOrAlias", [
            { no: 1, name: "model", kind: "enum", oneof: "choice", T: () => ["exa.seat_management_pb.Model", Model, "MODEL_"] },
            { no: 2, name: "alias", kind: "enum", oneof: "choice", T: () => ["exa.seat_management_pb.Alias", Alias] }
        ]);
    }
    create(value?: PartialMessage<ModelOrAlias>): ModelOrAlias {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.choice = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<ModelOrAlias>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ModelOrAlias): ModelOrAlias {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* exa.seat_management_pb.Model model */ 1:
                    message.choice = {
                        oneofKind: "model",
                        model: reader.int32()
                    };
                    break;
                case /* exa.seat_management_pb.Alias alias */ 2:
                    message.choice = {
                        oneofKind: "alias",
                        alias: reader.int32()
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ModelOrAlias, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* exa.seat_management_pb.Model model = 1; */
        if (message.choice.oneofKind === "model")
            writer.tag(1, WireType.Varint).int32(message.choice.model);
        /* exa.seat_management_pb.Alias alias = 2; */
        if (message.choice.oneofKind === "alias")
            writer.tag(2, WireType.Varint).int32(message.choice.alias);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.seat_management_pb.ModelOrAlias
 */
export const ModelOrAlias = new ModelOrAlias$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ClientModelConfig$Type extends MessageType<ClientModelConfig> {
    constructor() {
        super("exa.seat_management_pb.ClientModelConfig", [
            { no: 1, name: "label", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "model_or_alias", kind: "message", T: () => ModelOrAlias },
            { no: 3, name: "credit_multiplier", kind: "scalar", T: 2 /*ScalarType.FLOAT*/ },
            { no: 13, name: "pricing_type", kind: "enum", T: () => ["exa.seat_management_pb.ModelPricingType", ModelPricingType, "MODEL_PRICING_TYPE_"] },
            { no: 4, name: "disabled", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 5, name: "supports_images", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 6, name: "supports_legacy", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 7, name: "is_premium", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 8, name: "beta_warning_message", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 9, name: "is_beta", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 10, name: "provider", kind: "enum", T: () => ["exa.seat_management_pb.Provider", Provider] },
            { no: 11, name: "is_recommended", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 12, name: "allowed_tiers", kind: "enum", repeat: 1 /*RepeatType.PACKED*/, T: () => ["exa.seat_management_pb.TeamsTier", TeamsTier, "TEAMS_TIER_"] }
        ]);
    }
    create(value?: PartialMessage<ClientModelConfig>): ClientModelConfig {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.label = "";
        message.creditMultiplier = 0;
        message.pricingType = 0;
        message.disabled = false;
        message.supportsImages = false;
        message.supportsLegacy = false;
        message.isPremium = false;
        message.betaWarningMessage = "";
        message.isBeta = false;
        message.provider = 0;
        message.isRecommended = false;
        message.allowedTiers = [];
        if (value !== undefined)
            reflectionMergePartial<ClientModelConfig>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ClientModelConfig): ClientModelConfig {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string label */ 1:
                    message.label = reader.string();
                    break;
                case /* exa.seat_management_pb.ModelOrAlias model_or_alias */ 2:
                    message.modelOrAlias = ModelOrAlias.internalBinaryRead(reader, reader.uint32(), options, message.modelOrAlias);
                    break;
                case /* float credit_multiplier */ 3:
                    message.creditMultiplier = reader.float();
                    break;
                case /* exa.seat_management_pb.ModelPricingType pricing_type */ 13:
                    message.pricingType = reader.int32();
                    break;
                case /* bool disabled */ 4:
                    message.disabled = reader.bool();
                    break;
                case /* bool supports_images */ 5:
                    message.supportsImages = reader.bool();
                    break;
                case /* bool supports_legacy */ 6:
                    message.supportsLegacy = reader.bool();
                    break;
                case /* bool is_premium */ 7:
                    message.isPremium = reader.bool();
                    break;
                case /* string beta_warning_message */ 8:
                    message.betaWarningMessage = reader.string();
                    break;
                case /* bool is_beta */ 9:
                    message.isBeta = reader.bool();
                    break;
                case /* exa.seat_management_pb.Provider provider */ 10:
                    message.provider = reader.int32();
                    break;
                case /* bool is_recommended */ 11:
                    message.isRecommended = reader.bool();
                    break;
                case /* repeated exa.seat_management_pb.TeamsTier allowed_tiers */ 12:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.allowedTiers.push(reader.int32());
                    else
                        message.allowedTiers.push(reader.int32());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ClientModelConfig, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string label = 1; */
        if (message.label !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.label);
        /* exa.seat_management_pb.ModelOrAlias model_or_alias = 2; */
        if (message.modelOrAlias)
            ModelOrAlias.internalBinaryWrite(message.modelOrAlias, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* float credit_multiplier = 3; */
        if (message.creditMultiplier !== 0)
            writer.tag(3, WireType.Bit32).float(message.creditMultiplier);
        /* bool disabled = 4; */
        if (message.disabled !== false)
            writer.tag(4, WireType.Varint).bool(message.disabled);
        /* bool supports_images = 5; */
        if (message.supportsImages !== false)
            writer.tag(5, WireType.Varint).bool(message.supportsImages);
        /* bool supports_legacy = 6; */
        if (message.supportsLegacy !== false)
            writer.tag(6, WireType.Varint).bool(message.supportsLegacy);
        /* bool is_premium = 7; */
        if (message.isPremium !== false)
            writer.tag(7, WireType.Varint).bool(message.isPremium);
        /* string beta_warning_message = 8; */
        if (message.betaWarningMessage !== "")
            writer.tag(8, WireType.LengthDelimited).string(message.betaWarningMessage);
        /* bool is_beta = 9; */
        if (message.isBeta !== false)
            writer.tag(9, WireType.Varint).bool(message.isBeta);
        /* exa.seat_management_pb.Provider provider = 10; */
        if (message.provider !== 0)
            writer.tag(10, WireType.Varint).int32(message.provider);
        /* bool is_recommended = 11; */
        if (message.isRecommended !== false)
            writer.tag(11, WireType.Varint).bool(message.isRecommended);
        /* repeated exa.seat_management_pb.TeamsTier allowed_tiers = 12; */
        if (message.allowedTiers.length) {
            writer.tag(12, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.allowedTiers.length; i++)
                writer.int32(message.allowedTiers[i]);
            writer.join();
        }
        /* exa.seat_management_pb.ModelPricingType pricing_type = 13; */
        if (message.pricingType !== 0)
            writer.tag(13, WireType.Varint).int32(message.pricingType);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.seat_management_pb.ClientModelConfig
 */
export const ClientModelConfig = new ClientModelConfig$Type();
