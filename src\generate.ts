import {
  GetUserStatusResponse,
  PlanInfo,
  TeamConfig,
  UserStatus,
  ModelPricingType,
  ClientModelConfig as UserStatusClientModelConfig,
  Model as UserStatusModel,
  Provider as UserStatusProvider,
  TeamsTier,
} from './proto/user_status';
import {
  GetCommandModelConfigsResponse,
  ClientModelConfig as ModelConfigClientModelConfig,
  Model as ModelConfigModel,
  Provider as ModelConfigProvider,
} from './proto/model_config';

// 为 UserStatus 创建客户端模型配置
function createUserStatusClientModelConfigs(): UserStatusClientModelConfig[] {
  const commonConfig = {
    creditMultiplier: 1.0,
    pricingType: ModelPricingType.STATIC_CREDIT,
    disabled: false,
    supportsImages: true,
    supportsLegacy: true,
    isPremium: false, // 关键：设置为非高级版本，绕过权限检查
    betaWarningMessage: '',
    isBeta: false,
    isRecommended: true,
    allowedTiers: [
      TeamsTier.TEAMS,
      TeamsTier.PRO,
      TeamsTier.TRIAL,
      TeamsTier.ENTERPRISE_SAAS,
      TeamsTier.HYBRID,
      TeamsTier.ENTERPRISE_SELF_HOSTED,
      TeamsTier.WAITLIST_PRO,
      TeamsTier.TEAMS_ULTIMATE,
      TeamsTier.PRO_ULTIMATE,
    ],
  };

  return [
    // Google 模型系列
    {
      ...commonConfig,
      label: 'Gemini 1.0 Pro',
      provider: UserStatusProvider.MODEL_PROVIDER_GOOGLE,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.GOOGLE_GEMINI_1_0_PRO,
        },
      },
    },
    {
      ...commonConfig,
      label: 'Gemini 1.5 Pro',
      provider: UserStatusProvider.MODEL_PROVIDER_GOOGLE,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.GOOGLE_GEMINI_1_5_PRO,
        },
      },
    },
    {
      ...commonConfig,
      label: 'Gemini 2.0 Flash',
      provider: UserStatusProvider.MODEL_PROVIDER_GOOGLE,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.GOOGLE_GEMINI_2_0_FLASH,
        },
      },
    },
    {
      ...commonConfig,
      label: 'Gemini 2.5 Pro',
      provider: UserStatusProvider.MODEL_PROVIDER_GOOGLE,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.GOOGLE_GEMINI_2_5_PRO,
        },
      },
    },
    {
      ...commonConfig,
      label: 'Gemini 2.5 Flash',
      provider: UserStatusProvider.MODEL_PROVIDER_GOOGLE,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.GOOGLE_GEMINI_2_5_FLASH,
        },
      },
    },
    // Claude 模型系列
    {
      ...commonConfig,
      label: 'Claude 3.7 Sonnet',
      provider: UserStatusProvider.MODEL_PROVIDER_ANTHROPIC,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.CLAUDE_3_7_SONNET_20250219,
        },
      },
    },
    {
      ...commonConfig,
      label: 'Claude 4.0 Opus',
      provider: UserStatusProvider.MODEL_PROVIDER_ANTHROPIC,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.CLAUDE_4_OPUS,
        },
      },
    },
    {
      ...commonConfig,
      label: 'Claude 4.0 Sonnet',
      provider: UserStatusProvider.MODEL_PROVIDER_ANTHROPIC,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.CLAUDE_4_SONNET,
        },
      },
    },
    // DeepSeek 模型系列
    {
      ...commonConfig,
      label: 'DeepSeek V3',
      provider: UserStatusProvider.MODEL_PROVIDER_DEEPSEEK,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.DEEPSEEK_V3,
        },
      },
    },
    {
      ...commonConfig,
      label: 'DeepSeek R1',
      provider: UserStatusProvider.MODEL_PROVIDER_DEEPSEEK,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.DEEPSEEK_R1,
        },
      },
    },
    {
      ...commonConfig,
      label: 'DeepSeek R1 Fast',
      provider: UserStatusProvider.MODEL_PROVIDER_DEEPSEEK,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.DEEPSEEK_R1_FAST,
        },
      },
    },
    {
      ...commonConfig,
      label: 'DeepSeek R1 Slow',
      provider: UserStatusProvider.MODEL_PROVIDER_DEEPSEEK,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.DEEPSEEK_R1_SLOW,
        },
      },
    },
    // OpenAI 模型系列
    {
      ...commonConfig,
      label: 'O3 Pro 2025-06-10',
      provider: UserStatusProvider.MODEL_PROVIDER_OPENAI,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.O3_PRO_2025_06_10,
        },
      },
    },
    {
      ...commonConfig,
      label: 'O3 Pro 2025-06-10 Low',
      provider: UserStatusProvider.MODEL_PROVIDER_OPENAI,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.O3_PRO_2025_06_10_LOW,
        },
      },
    },
    {
      ...commonConfig,
      label: 'O3 Pro 2025-06-10 High',
      provider: UserStatusProvider.MODEL_PROVIDER_OPENAI,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: UserStatusModel.O3_PRO_2025_06_10_HIGH,
        },
      },
    }
  ];
}

// 为 GetCommandModelConfigs 创建客户端模型配置
function createModelConfigClientModelConfigs(): ModelConfigClientModelConfig[] {
  const commonConfig = {
    creditMultiplier: 1.0,
    disabled: false,
    supportsImages: true,
    supportsLegacy: true,
    isPremium: false, // 关键：设置为非高级版本，绕过权限检查
    betaWarningMessage: '',
    isBeta: false,
    isRecommended: true,
    allowedTiers: [1, 2, 3, 4, 5, 6, 7, 8], // 使用数字，对应所有等级
  };

  return [
    // Google 模型系列
    {
      ...commonConfig,
      label: 'Gemini 1.0 Pro',
      provider: ModelConfigProvider.MODEL_PROVIDER_GOOGLE,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_GOOGLE_GEMINI_1_0_PRO,
        },
      },
    },
    {
      ...commonConfig,
      label: 'Gemini 1.5 Pro',
      provider: ModelConfigProvider.MODEL_PROVIDER_GOOGLE,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_GOOGLE_GEMINI_1_5_PRO,
        },
      },
    },
    {
      ...commonConfig,
      label: 'Gemini 2.0 Flash',
      provider: ModelConfigProvider.MODEL_PROVIDER_GOOGLE,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_GOOGLE_GEMINI_2_0_FLASH,
        },
      },
    },
    {
      ...commonConfig,
      label: 'Gemini 2.5 Pro',
      provider: ModelConfigProvider.MODEL_PROVIDER_GOOGLE,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_GOOGLE_GEMINI_2_5_PRO,
        },
      },
    },
    {
      ...commonConfig,
      label: 'Gemini 2.5 Flash',
      provider: ModelConfigProvider.MODEL_PROVIDER_GOOGLE,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_GOOGLE_GEMINI_2_5_FLASH,
        },
      },
    },
    {
      ...commonConfig,
      label: 'Gemini 2.5 Flash Thinking',
      provider: ModelConfigProvider.MODEL_PROVIDER_GOOGLE,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_GOOGLE_GEMINI_2_5_FLASH_THINKING,
        },
      },
    },
    {
      ...commonConfig,
      label: 'Gemini Exp 1206',
      provider: ModelConfigProvider.MODEL_PROVIDER_GOOGLE,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_GOOGLE_GEMINI_EXP_1206,
        },
      },
    },
    // Claude 模型系列
    {
      ...commonConfig,
      label: 'Claude 3.7 Sonnet',
      provider: ModelConfigProvider.MODEL_PROVIDER_ANTHROPIC,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_CLAUDE_3_7_SONNET_20250219,
        },
      },
    },
    {
      ...commonConfig,
      label: 'Claude 4.0 Opus',
      provider: ModelConfigProvider.MODEL_PROVIDER_ANTHROPIC,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_CLAUDE_4_OPUS,
        },
      },
    },
    {
      ...commonConfig,
      label: 'Claude 4.0 Sonnet',
      provider: ModelConfigProvider.MODEL_PROVIDER_ANTHROPIC,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_CLAUDE_4_SONNET,
        },
      },
    },
    // DeepSeek 模型系列
    {
      ...commonConfig,
      label: 'DeepSeek V3',
      provider: ModelConfigProvider.MODEL_PROVIDER_DEEPSEEK,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_DEEPSEEK_V3,
        },
      },
    },
    {
      ...commonConfig,
      label: 'DeepSeek R1',
      provider: ModelConfigProvider.MODEL_PROVIDER_DEEPSEEK,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_DEEPSEEK_R1,
        },
      },
    },
    {
      ...commonConfig,
      label: 'DeepSeek R1 Fast',
      provider: ModelConfigProvider.MODEL_PROVIDER_DEEPSEEK,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_DEEPSEEK_R1_FAST,
        },
      },
    },
    {
      ...commonConfig,
      label: 'DeepSeek R1 Slow',
      provider: ModelConfigProvider.MODEL_PROVIDER_DEEPSEEK,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_DEEPSEEK_R1_SLOW,
        },
      },
    },
    // OpenAI 模型系列
    {
      ...commonConfig,
      label: 'O3 Pro 2025-06-10',
      provider: ModelConfigProvider.MODEL_PROVIDER_OPENAI,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_O3_PRO_2025_06_10,
        },
      },
    },
    {
      ...commonConfig,
      label: 'O3 Pro 2025-06-10 Low',
      provider: ModelConfigProvider.MODEL_PROVIDER_OPENAI,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_O3_PRO_2025_06_10_LOW,
        },
      },
    },
    {
      ...commonConfig,
      label: 'O3 Pro 2025-06-10 High',
      provider: ModelConfigProvider.MODEL_PROVIDER_OPENAI,
      modelOrAlias: {
        choice: {
          oneofKind: 'model' as const,
          model: ModelConfigModel.MODEL_O3_PRO_2025_06_10_HIGH,
        },
      },
    },
  ];
}

// 用户状态配置
const planConfig: PlanInfo = {
  teamsTier: 8, // 团队等级 (no: 1)
  planName: 'Pro Ultimate', // 计划名称 (no: 2)
  hasAutocompleteFastMode: true, // 是否启用快速自动完成模式 (no: 3)
  allowStickyPremiumModels: true, // 是否允许固定高级模型 (no: 4)
  hasForgeAccess: true, // 是否有Forge访问权限 (no: 5)
  maxNumPremiumChatMessages: BigInt(999999), // 高级聊天消息数量上限 (no: 6)
  maxNumChatInputTokens: BigInt(999999), // 聊天输入令牌数量上限 (no: 7)
  maxCustomChatInstructionCharacters: BigInt(999999), // 自定义聊天指令字符数上限 (no: 8)
  maxNumPinnedContextItems: BigInt(999999), // 固定上下文项数量上限 (no: 9)
  maxLocalIndexSize: BigInt(999999), // 本地索引大小上限 (no: 10)
  disableCodeSnippetTelemetry: false, // 是否禁用代码片段遥测 (no: 11)
  monthlyPromptCredits: 999900, // 每月提示点数 (no: 12)
  monthlyFlowCredits: 999900, // 每月流程点数 (no: 13)
  monthlyFlexCreditPurchaseAmount: 0, // 每月弹性点数购买额度 (no: 14)
  allowPremiumCommandModels: true, // 是否允许高级命令模型 (no: 15)
  isEnterprise: false, // 是否为企业版 (no: 16)
  isTeams: false, // 是否为团队版 (no: 17)
  canBuyMoreCredits: true, // 是否可以购买更多点数 (no: 18)
  cascadeWebSearchEnabled: true, // 是否启用级联网络搜索 (no: 19)
  canCustomizeAppIcon: true, // 是否可以自定义应用图标 (no: 20)
  cascadeAllowedModelsConfig: [], // 允许的级联模型配置列表 (no: 21)
  cascadeCanAutoRunCommands: true, // 是否允许级联自动运行命令 (no: 22)
  hasTabToJump: true, // 是否启用tab跳转功能 (no: 23)
  defaultTeamConfig: {
    // 默认团队配置 (no: 24)
    teamId: '',
    userPromptCreditCap: BigInt(666600),
    userFlowCreditCap: BigInt(666600),
    autoProvisionCascadeSeat: true,
    allowMcpServers: true,
    allowAutoRunCommands: true,
    allowCustomRecipes: true,
    maxUnclaimedSites: BigInt(999999),
    allowAppDeployments: true,
    maxNewSitesPerDay: BigInt(999999),
  } as TeamConfig,
  canGenerateCommitMessages: true, // 是否可以生成提交消息 (no: 25)
  maxUnclaimedSites: 999999, // 最大未认领站点数 (no: 26) update 2025-06-21 windsurf 1.10.5
  // update 2025-05-12 windsurf 1.8.2
  knowledgeBaseEnabled: false, // 是否启用知识库 (no: 27) [暂时无权限]
  canShareConversations: true, // 是否可以分享对话 (no: 28)
  canAllowCascadeInBackground: true, // 是否可以允许级联在后台运行 (no: 29)
  // update 2025-06-21 windsurf 1.10.5 新增字段
  defaultTeamFeatures: {}, // 默认团队功能配置映射 (no: 30) [不生成团队功能]
  browserEnabled: true, // 是否启用浏览器功能 (no: 31)
};

// 生成用户状态的hex 返回buffer
export function generateUserStatusHex(): Buffer {
  const userStatus: UserStatus = {
    pro: true,
    name: 'cipher',
    email: '<EMAIL>',
    planInfo: planConfig,
    planStatus: {
      planInfo: planConfig,
      planStart: { seconds: BigInt(1735660800), nanos: 0 },
      planEnd: { seconds: BigInt(4070880000), nanos: 0 },
      availableFlexCredits: 666600,
      usedFlowCredits: 0,
      usedPromptCredits: 0,
      usedFlexCredits: 0,
      availablePromptCredits: 666600,
      availableFlowCredits: 666600,
    },
    teamStatus: 2,
    teamConfig: planConfig.defaultTeamConfig,
    hasUsedWindsurf: false,
    userUsedPromptCredits: BigInt(0),
    userUsedFlowCredits: BigInt(0),
    hasFingerprintSet: true,
    // update 2025-06-21 windsurf 1.11.0 新增字段
    cascadeModelConfigData: {
      clientModelConfigs: createUserStatusClientModelConfigs(), // 客户端模型配置列表 (no: 1) [与 GetCommandModelConfigs 保持一致]
      clientModelSorts: [], // 客户端模型排序列表 (no: 2) [不生成排序功能]
      defaultOverrideModelConfig: undefined, // 默认覆盖模型配置 (no: 3) [可选，不设置]
    }, // 级联模型配置数据 (no: 33)
    // 以下字段在proto中没有，但是需要设置
    disableTelemetry: false,
    ignoreChatTelemetrySetting: false,
    teamId: '',
    teamsFeatures: [],
    userFeatures: [],
    permissions: [],
  };

  const response: GetUserStatusResponse = {
    userStatus,
    planInfo: planConfig,
  };

  const buffer = Buffer.from(GetUserStatusResponse.toBinary(response));
  console.log('Buffer length:', buffer.length);
  return buffer;
}

// getcommand model config
export function generateCommandModelConfigHex(): Buffer {
  // 模型配置 - 使用与 cascadeModelConfigData 相同的配置
  const modelConfigResponse: GetCommandModelConfigsResponse = {
    clientModelConfigs: createModelConfigClientModelConfigs(),
  };

  // 序列化为二进制
  const writer = GetCommandModelConfigsResponse.toBinary(modelConfigResponse);
  const buffer = Buffer.from(writer);
  return buffer;
}
